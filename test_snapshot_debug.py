#!/usr/bin/env python3
"""
Debug script to test snapshot API endpoints and understand the path parameter issue.
"""

import json
import time
import boto3
import requests
from datetime import datetime, timezone

# Configuration
API_BASE_URL = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
COGNITO_CLIENT_ID = "2jh76f894g6lv9vrus4qbb9hu7"
USER_POOL_ID = "ap-northeast-1_H2kKHGUAT"
TEST_USERNAME = "<EMAIL>"
TEST_PASSWORD = "TestPassword123!"


def authenticate():
    """Authenticate with Cognito to get JWT token"""
    print("🔐 Authenticating with AWS Cognito...")

    cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")

    try:
        response = cognito_client.admin_initiate_auth(
            UserPoolId=USER_POOL_ID,
            ClientId=COGNITO_CLIENT_ID,
            AuthFlow="ADMIN_NO_SRP_AUTH",
            AuthParameters={"USERNAME": TEST_USERNAME, "PASSWORD": TEST_PASSWORD},
        )

        if "AuthenticationResult" in response:
            auth_result = response["AuthenticationResult"]
            id_token = auth_result.get("IdToken")

            if id_token:
                print("✅ Successfully authenticated")
                return id_token
            else:
                print("❌ No ID token received")
                return None
        else:
            print("❌ Authentication failed")
            return None

    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None


def create_test_fund(jwt_token):
    """Create a test fund to use for snapshot testing"""
    print("\n📝 Creating test fund...")

    fund_id = f"DEBUG-{int(time.time())}"

    fund_data = {
        "fund_id": fund_id,
        "name": "Debug Test Fund",
        "symbol": f"DBG{int(time.time()) % 1000}",
        "fund_type": "etf",
        "category": "Equity",
        "description": "Test fund for debugging snapshot API",
        "nav": 100.0,
        "previous_nav": 99.50,
        "minimum_investment": 1000.0,
        "expense_ratio": 0.75,
        "total_assets": 50000000.0,
        "inception_date": "2020-01-01T00:00:00Z",
        "risk_level": "moderate",
        "status": "active",
    }

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {jwt_token}",
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/funds",
            headers=headers,
            json=fund_data,
            timeout=30,
        )

        if response.status_code in [200, 201]:
            print(f"✅ Test fund created with ID: {fund_id}")
            return fund_id
        else:
            print(f"❌ Error creating fund: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return None

    except Exception as e:
        print(f"❌ Error creating fund: {e}")
        return None


def test_snapshot_creation(jwt_token, fund_id):
    """Test snapshot creation with detailed debugging"""
    print(f"\n📅 Testing snapshot creation for fund: {fund_id}")

    test_month = (
        f"{datetime.now(timezone.utc).year}-{datetime.now(timezone.utc).month:02d}"
    )

    snapshot_data = {
        "nav": 112.50,
        "total_assets": 85000000.0,
        "notes": f"Debug test snapshot for {test_month}",
    }

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {jwt_token}",
    }

    # Test different URL formats to see which one works
    url_formats = [
        f"{API_BASE_URL}/funds/{fund_id}/snapshots/{test_month}",
        f"{API_BASE_URL}/funds/{fund_id}/snapshots/{test_month}/",
    ]

    for i, url in enumerate(url_formats):
        print(f"\n🔍 Testing URL format {i+1}: {url}")

        try:
            response = requests.post(
                url,
                headers=headers,
                json=snapshot_data,
                timeout=30,
            )

            print(f"📊 Status Code: {response.status_code}")
            print(f"📄 Response Headers: {dict(response.headers)}")
            print(f"📄 Response Body: {response.text}")

            if response.status_code in [200, 201]:
                print(f"✅ Snapshot creation successful with URL format {i+1}")
                return True
            else:
                print(f"❌ Snapshot creation failed with URL format {i+1}")

        except Exception as e:
            print(f"❌ Error with URL format {i+1}: {e}")

    return False


def test_snapshot_listing(jwt_token, fund_id):
    """Test snapshot listing with detailed debugging"""
    print(f"\n📋 Testing snapshot listing for fund: {fund_id}")

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {jwt_token}",
    }

    # Test different URL formats
    url_formats = [
        f"{API_BASE_URL}/funds/{fund_id}/snapshots",
        f"{API_BASE_URL}/funds/{fund_id}/snapshots/",
    ]

    for i, url in enumerate(url_formats):
        print(f"\n🔍 Testing URL format {i+1}: {url}")

        try:
            response = requests.get(
                url,
                headers=headers,
                timeout=30,
            )

            print(f"📊 Status Code: {response.status_code}")
            print(f"📄 Response Headers: {dict(response.headers)}")
            print(f"📄 Response Body: {response.text}")

            if response.status_code == 200:
                print(f"✅ Snapshot listing successful with URL format {i+1}")
                return True
            else:
                print(f"❌ Snapshot listing failed with URL format {i+1}")

        except Exception as e:
            print(f"❌ Error with URL format {i+1}: {e}")

    return False


def cleanup_test_fund(jwt_token, fund_id):
    """Clean up the test fund"""
    print(f"\n🧹 Cleaning up test fund: {fund_id}")

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {jwt_token}",
    }

    try:
        response = requests.delete(
            f"{API_BASE_URL}/funds/{fund_id}",
            headers=headers,
            timeout=30,
        )

        if response.status_code in [200, 204]:
            print("✅ Test fund cleaned up successfully")
        else:
            print(f"⚠️  Could not clean up test fund: {response.status_code}")
            print(f"📄 Response: {response.text}")

    except Exception as e:
        print(f"⚠️  Error cleaning up test fund: {e}")


def test_regular_fund_endpoint(jwt_token, fund_id):
    """Test regular fund endpoint to see if path parameters work there"""
    print(f"\n🔍 Testing regular fund endpoint for comparison: {fund_id}")

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {jwt_token}",
    }

    try:
        response = requests.get(
            f"{API_BASE_URL}/funds/{fund_id}",
            headers=headers,
            timeout=30,
        )

        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        if response.status_code == 200:
            result = response.json()
            print(
                f"📄 Response Body (truncated): fund_id={result.get('fund_id', 'NOT_FOUND')}"
            )
            print("✅ Regular fund endpoint works correctly")
            return True
        else:
            print(f"📄 Response Body: {response.text}")
            print("❌ Regular fund endpoint failed")
            return False

    except Exception as e:
        print(f"❌ Error with regular fund endpoint: {e}")
        return False


def main():
    """Main debug function"""
    print("🚀 Starting snapshot API debug test...")
    print("=" * 60)

    # Authenticate
    jwt_token = authenticate()
    if not jwt_token:
        print("❌ Authentication failed - cannot proceed")
        return

    # Create test fund
    fund_id = create_test_fund(jwt_token)
    if not fund_id:
        print("❌ Fund creation failed - cannot proceed")
        return

    try:
        # Test regular fund endpoint for comparison
        regular_success = test_regular_fund_endpoint(jwt_token, fund_id)

        # Test snapshot creation
        creation_success = test_snapshot_creation(jwt_token, fund_id)

        # Test snapshot listing
        listing_success = test_snapshot_listing(jwt_token, fund_id)

        # Summary
        print("\n" + "=" * 60)
        print("📋 DEBUG TEST SUMMARY")
        print("=" * 60)
        print(f"Authentication:      ✅ PASS")
        print(f"Fund Creation:       ✅ PASS")
        print(f"Regular Fund GET:    {'✅ PASS' if regular_success else '❌ FAIL'}")
        print(f"Snapshot Creation:   {'✅ PASS' if creation_success else '❌ FAIL'}")
        print(f"Snapshot Listing:    {'✅ PASS' if listing_success else '❌ FAIL'}")
        print("=" * 60)

        if regular_success and not creation_success:
            print(
                "🔍 ANALYSIS: Regular fund endpoints work but snapshot endpoints don't."
            )
            print(
                "   This confirms the issue is with path parameter extraction in snapshot handlers."
            )

    finally:
        # Always clean up
        cleanup_test_fund(jwt_token, fund_id)


if __name__ == "__main__":
    main()
