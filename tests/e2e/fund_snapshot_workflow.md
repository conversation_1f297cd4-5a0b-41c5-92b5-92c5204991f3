# End-to-End Testing Guide for Monthly Fund Snapshots

This document outlines comprehensive end-to-end testing scenarios for the monthly fund snapshot functionality.

## Test Environment Setup

### Prerequisites
1. AWS environment with deployed stack
2. Test fund data in the database
3. Valid user authentication tokens
4. Frontend application running locally or deployed

### Test Data
- Test Fund ID: `test-fund-e2e-123`
- Test User: `<EMAIL>`
- Test Months: `2024-12`, `2024-11`, `2024-10`

## Test Scenarios

### Scenario 1: Complete Snapshot Creation Workflow

**Objective**: Test the full workflow of creating a monthly snapshot with all data types.

**Steps**:
1. **Navigate to Fund Edit Page**
   - Go to `/funds/test-fund-e2e-123/edit`
   - Verify page loads with fund details
   - Verify tabs are available: Basic Info, Market Data, Holdings, KPI & Risk Metrics

2. **Create Market Data Snapshot**
   - Click on "Market Data" tab
   - Select month "2024-12" using MonthSelector
   - Verify MonthSelector shows:
     - Current month indicator
     - Available months with data indicators
     - Proper month formatting
   - Fill in market data fields:
     - NAV: 150.75
     - Total Assets: 1,000,000
     - Price data, volume, etc.
   - Submit form
   - Verify success message: "Market data snapshot created successfully for 2024-12!"
   - Verify MonthSelector now shows 2024-12 as having data

3. **Create Holdings Snapshot**
   - Click on "Holdings" tab
   - Select same month "2024-12"
   - Verify form loads with MonthSelector showing existing data indicator
   - Add top holdings:
     - Apple Inc (AAPL): 5.2%
     - Microsoft Corp (MSFT): 4.8%
   - Set sector allocations:
     - Technology: 25.5%
     - Healthcare: 18.3%
   - Submit form
   - Verify success message includes month reference

4. **Create KPI & Risk Metrics Snapshot**
   - Click on "KPI & Risk Metrics" tab
   - Select same month "2024-12"
   - Fill in KPI fields:
     - Total Return: 12.5%
     - Annualized Return: 8.7%
     - Sharpe Ratio: 1.2
   - Fill in risk metrics:
     - Standard Deviation: 15.2%
     - VaR: 2.1%
   - Submit form
   - Verify success message

**Expected Results**:
- All three snapshots created successfully for 2024-12
- MonthSelector consistently shows 2024-12 as having data
- No errors or validation issues
- Success messages are clear and informative

### Scenario 2: Snapshot Data Loading and Editing

**Objective**: Test loading existing snapshot data and editing capabilities.

**Steps**:
1. **Load Existing Market Data**
   - Navigate to Market Data tab
   - Select month "2024-12" (created in Scenario 1)
   - Verify form pre-populates with existing data
   - Verify loading indicator appears briefly
   - Verify status message: "✓ Data exists for 2024-12 - editing existing snapshot"

2. **Edit Market Data**
   - Change NAV from 150.75 to 152.30
   - Add notes: "Updated with latest market close"
   - Submit form
   - Verify success message indicates update

3. **Verify Data Persistence**
   - Refresh page
   - Navigate back to Market Data tab
   - Select month "2024-12"
   - Verify NAV shows 152.30
   - Verify notes are preserved

**Expected Results**:
- Existing data loads correctly
- Edits are saved and persist
- Loading states work properly
- Status indicators are accurate

### Scenario 3: Fund Detail Page Integration

**Objective**: Test snapshot data display on fund detail page.

**Steps**:
1. **Navigate to Fund Detail Page**
   - Go to `/funds/test-fund-e2e-123`
   - Verify page loads with fund information

2. **Verify Snapshot Information Display**
   - Look for "Monthly Snapshot Data" card
   - Verify it shows:
     - Data Month: December 2024
     - Last Updated: Current date
     - Updated By: <EMAIL>
     - Available Months: 1

3. **Verify Monthly Data Availability**
   - Look for "Monthly Data Availability" section
   - Verify it shows "Dec 2024" badge
   - Verify badge styling indicates data availability

4. **Verify Enhanced Fund Data**
   - Check if fund metrics reflect latest snapshot data
   - Verify NAV shows 152.30 (updated value)
   - Verify holdings data is current

**Expected Results**:
- Snapshot information is clearly displayed
- Data availability is visually indicated
- Fund details reflect latest snapshot data
- UI is clean and informative

### Scenario 4: Multiple Month Management

**Objective**: Test managing snapshots across multiple months.

**Steps**:
1. **Create Additional Months**
   - Create snapshots for 2024-11 and 2024-10
   - Use different data values for each month
   - Verify each month creation is successful

2. **Test Month Navigation**
   - Navigate between different months using MonthSelector
   - Verify data loads correctly for each month
   - Verify status indicators update appropriately

3. **Test Data Availability Display**
   - Check fund detail page
   - Verify "Available Months: 3"
   - Verify all three months show in availability badges
   - Verify months are sorted correctly (most recent first)

**Expected Results**:
- Multiple months can be managed independently
- Month navigation works smoothly
- Data availability is accurately tracked
- UI handles multiple months gracefully

### Scenario 5: Error Handling and Validation

**Objective**: Test error handling and data validation.

**Steps**:
1. **Test Invalid Month Selection**
   - Try to create snapshot for future month
   - Verify appropriate error message

2. **Test Invalid Data Submission**
   - Submit market data with invalid NAV (negative number)
   - Submit holdings with percentages > 100%
   - Verify validation errors are displayed clearly

3. **Test Network Error Handling**
   - Simulate network failure during submission
   - Verify error message is user-friendly
   - Verify form state is preserved

4. **Test Unauthorized Access**
   - Test with expired/invalid session
   - Verify redirect to login
   - Verify error messages are appropriate

**Expected Results**:
- All validation errors are caught and displayed
- Error messages are clear and actionable
- Form state is preserved during errors
- Security is properly enforced

### Scenario 6: Snapshot Deletion Workflow

**Objective**: Test snapshot deletion functionality.

**Steps**:
1. **Delete via API** (using API client or admin interface)
   - Delete 2024-10 snapshot
   - Verify deletion success

2. **Verify UI Updates**
   - Navigate to fund edit page
   - Verify MonthSelector no longer shows 2024-10 as having data
   - Verify fund detail page shows "Available Months: 2"
   - Verify 2024-10 badge is removed from availability display

3. **Test Deletion of Non-existent Snapshot**
   - Try to delete already deleted snapshot
   - Verify appropriate error handling

**Expected Results**:
- Deletion works correctly
- UI updates reflect deletion immediately
- Error handling for non-existent snapshots works
- Data consistency is maintained

## Performance Testing

### Load Testing Scenarios
1. **Concurrent Snapshot Creation**
   - Multiple users creating snapshots simultaneously
   - Verify no data corruption or conflicts

2. **Large Dataset Handling**
   - Create snapshots with large holdings lists (100+ holdings)
   - Verify performance remains acceptable

3. **Historical Data Loading**
   - Load fund with 12+ months of snapshot data
   - Verify page load times are reasonable

## Accessibility Testing

### Key Areas to Test
1. **MonthSelector Component**
   - Keyboard navigation works
   - Screen reader compatibility
   - Focus management

2. **Form Accessibility**
   - All form fields have proper labels
   - Error messages are announced
   - Tab order is logical

3. **Data Display**
   - Snapshot information is accessible
   - Data availability indicators work with screen readers

## Browser Compatibility

### Test Browsers
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

### Mobile Testing
- iOS Safari
- Android Chrome
- Responsive design verification

## Automated Testing Integration

### Cypress E2E Tests
```javascript
// Example test structure
describe('Fund Snapshot E2E', () => {
  it('should complete full snapshot creation workflow', () => {
    // Implementation of Scenario 1
  });
  
  it('should handle snapshot editing correctly', () => {
    // Implementation of Scenario 2
  });
  
  // Additional test cases...
});
```

### Test Data Management
- Use database seeding for consistent test data
- Clean up test data after each test run
- Use isolated test environment

## Success Criteria

### Functional Requirements
- ✅ All CRUD operations work correctly
- ✅ Data validation is comprehensive
- ✅ UI updates reflect backend changes
- ✅ Error handling is robust

### Performance Requirements
- ✅ Page load times < 3 seconds
- ✅ Form submissions < 2 seconds
- ✅ Smooth month navigation
- ✅ No memory leaks

### User Experience Requirements
- ✅ Intuitive month selection
- ✅ Clear data availability indicators
- ✅ Informative success/error messages
- ✅ Consistent UI behavior

### Security Requirements
- ✅ Proper authentication enforcement
- ✅ Data validation on both client and server
- ✅ No unauthorized data access
- ✅ Secure API endpoints

## Test Execution Checklist

- [ ] Environment setup complete
- [ ] Test data prepared
- [ ] All scenarios executed
- [ ] Performance testing completed
- [ ] Accessibility testing completed
- [ ] Browser compatibility verified
- [ ] Mobile testing completed
- [ ] Security testing completed
- [ ] Documentation updated
- [ ] Test results documented
