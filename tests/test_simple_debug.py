#!/usr/bin/env python3
"""
Simple debug test to check imports and basic functionality
"""

import sys
import os

print("🔧 Starting simple debug test...")

# Add the src directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
src_path = os.path.join(project_root, "src")
sys.path.insert(0, src_path)
print(f"🔧 Added to Python path: {src_path}")

# Test basic imports
print("🔄 Testing basic imports...")

try:
    import boto3
    print("✅ boto3 imported")
except Exception as e:
    print(f"❌ boto3 import failed: {e}")

try:
    import requests
    print("✅ requests imported")
except Exception as e:
    print(f"❌ requests import failed: {e}")

try:
    from datetime import datetime, timezone
    print("✅ datetime imported")
except Exception as e:
    print(f"❌ datetime import failed: {e}")

# Test validation service import
print("🔄 Testing validation service import...")
try:
    from shared.validation.fund_validation import FundValidationService
    print("✅ Validation service imported")
    VALIDATION_AVAILABLE = True
except Exception as e:
    print(f"❌ Validation service import failed: {e}")
    VALIDATION_AVAILABLE = False

# Test fund models import
print("🔄 Testing fund models import...")
try:
    from shared.models.fund import Fund, FundCreate, FundUpdate, FundDynamoDBItem
    print("✅ Fund models imported")
    FUND_MODELS_AVAILABLE = True
except Exception as e:
    print(f"❌ Fund models import failed: {e}")
    FUND_MODELS_AVAILABLE = False

# Test snapshot models import
print("🔄 Testing snapshot models import...")
try:
    from shared.models.fund_snapshot import FundSnapshot, FundSnapshotCreate, FundSnapshotUpdate
    print("✅ Snapshot models imported")
    SNAPSHOT_MODELS_AVAILABLE = True
except Exception as e:
    print(f"❌ Snapshot models import failed: {e}")
    SNAPSHOT_MODELS_AVAILABLE = False

# Test AWS connection
print("🔄 Testing AWS connection...")
try:
    dynamodb = boto3.resource("dynamodb", region_name="ap-northeast-1")
    funds_table = dynamodb.Table("fundflow-dev-funds")
    print("✅ DynamoDB connection established")
    AWS_AVAILABLE = True
except Exception as e:
    print(f"❌ AWS connection failed: {e}")
    AWS_AVAILABLE = False

# Summary
print("\n" + "=" * 50)
print("📋 IMPORT TEST SUMMARY")
print("=" * 50)
print(f"Validation Service: {'✅' if VALIDATION_AVAILABLE else '❌'}")
print(f"Fund Models: {'✅' if FUND_MODELS_AVAILABLE else '❌'}")
print(f"Snapshot Models: {'✅' if SNAPSHOT_MODELS_AVAILABLE else '❌'}")
print(f"AWS Connection: {'✅' if AWS_AVAILABLE else '❌'}")

if VALIDATION_AVAILABLE and FUND_MODELS_AVAILABLE and AWS_AVAILABLE:
    print("\n🎉 All core components available - ready for testing!")
else:
    print("\n⚠️  Some components unavailable - tests may be limited")

print("=" * 50)
