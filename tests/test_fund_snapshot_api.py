"""
Integration tests for Fund Snapshot API endpoints.
"""

import json
import pytest
from unittest.mock import Mock, patch
from decimal import Decimal

from src.functions.api.funds import lambda_handler
from src.shared.models.fund_snapshot import FundSnapshot


class TestFundSnapshotAPI:
    """Test cases for Fund Snapshot API endpoints."""

    @pytest.fixture
    def mock_session_manager(self):
        """Mock session manager for authentication."""
        with patch('src.functions.api.funds.SessionManager') as mock:
            session_manager = Mock()
            session_manager.validate_session.return_value = {
                "valid": True,
                "user_info": {
                    "user_id": "test-user-123",
                    "email": "<EMAIL>"
                }
            }
            mock.return_value = session_manager
            yield session_manager

    @pytest.fixture
    def mock_snapshot_repository(self):
        """Mock fund snapshot repository."""
        with patch('src.functions.api.funds.get_fund_snapshot_repository') as mock:
            repo = Mock()
            mock.return_value = repo
            yield repo

    @pytest.fixture
    def mock_fund_repository(self):
        """Mock fund repository."""
        with patch('src.functions.api.funds.get_fund_repository') as mock:
            repo = Mock()
            mock.return_value = repo
            yield repo

    @pytest.fixture
    def sample_snapshot(self):
        """Sample snapshot for testing."""
        return FundSnapshot(
            fund_id="test-fund-123",
            snapshot_month="2024-12",
            nav=Decimal("150.75"),
            total_assets=Decimal("1000000.00"),
            market_data={"price": 150.75, "volume": 10000},
            created_at="2024-12-01T10:00:00Z",
            updated_at="2024-12-01T10:00:00Z",
            created_by="test-user-123",
            notes="Test snapshot"
        )

    def test_create_fund_snapshot_success(self, mock_session_manager, mock_snapshot_repository, mock_fund_repository, sample_snapshot):
        """Test successful fund snapshot creation."""
        # Mock fund exists
        mock_fund_repository.get_fund.return_value = Mock(id="test-fund-123")
        
        # Mock snapshot creation
        mock_snapshot_repository.create_or_update_snapshot.return_value = sample_snapshot
        
        # Create test event
        event = {
            "httpMethod": "POST",
            "path": "/funds/test-fund-123/snapshots/2024-12",
            "pathParameters": {"id": "test-fund-123", "month": "2024-12"},
            "headers": {"Authorization": "Bearer test-token"},
            "body": json.dumps({
                "nav": "150.75",
                "total_assets": "1000000.00",
                "market_data": {"price": 150.75, "volume": 10000},
                "notes": "Test snapshot"
            })
        }
        
        # Call the handler
        response = lambda_handler(event, {})
        
        # Verify response
        assert response["statusCode"] == 201
        body = json.loads(response["body"])
        assert body["success"] is True
        assert "data" in body
        
        # Verify repository was called correctly
        mock_snapshot_repository.create_or_update_snapshot.assert_called_once()

    def test_get_fund_snapshot_success(self, mock_session_manager, mock_snapshot_repository, sample_snapshot):
        """Test successful fund snapshot retrieval."""
        # Mock snapshot retrieval
        mock_snapshot_repository.get_snapshot.return_value = sample_snapshot
        
        # Create test event
        event = {
            "httpMethod": "GET",
            "path": "/funds/test-fund-123/snapshots/2024-12",
            "pathParameters": {"id": "test-fund-123", "month": "2024-12"},
            "headers": {"Authorization": "Bearer test-token"}
        }
        
        # Call the handler
        response = lambda_handler(event, {})
        
        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["success"] is True
        assert body["data"]["fund_id"] == "test-fund-123"
        assert body["data"]["snapshot_month"] == "2024-12"
        
        # Verify repository was called correctly
        mock_snapshot_repository.get_snapshot.assert_called_once_with("test-fund-123", "2024-12")

    def test_get_fund_snapshot_not_found(self, mock_session_manager, mock_snapshot_repository):
        """Test fund snapshot retrieval when snapshot doesn't exist."""
        # Mock snapshot not found
        mock_snapshot_repository.get_snapshot.return_value = None
        
        # Create test event
        event = {
            "httpMethod": "GET",
            "path": "/funds/test-fund-123/snapshots/2024-12",
            "pathParameters": {"id": "test-fund-123", "month": "2024-12"},
            "headers": {"Authorization": "Bearer test-token"}
        }
        
        # Call the handler
        response = lambda_handler(event, {})
        
        # Verify response
        assert response["statusCode"] == 404
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "not found" in body["message"].lower()

    def test_list_fund_snapshots_success(self, mock_session_manager, mock_snapshot_repository, sample_snapshot):
        """Test successful fund snapshots listing."""
        # Mock snapshots listing
        mock_snapshot_repository.list_snapshots.return_value = {
            "snapshots": [sample_snapshot],
            "count": 1
        }
        
        # Create test event
        event = {
            "httpMethod": "GET",
            "path": "/funds/test-fund-123/snapshots",
            "pathParameters": {"id": "test-fund-123"},
            "queryStringParameters": {"start_month": "2024-01", "end_month": "2024-12", "limit": "10"},
            "headers": {"Authorization": "Bearer test-token"}
        }
        
        # Call the handler
        response = lambda_handler(event, {})
        
        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["success"] is True
        assert body["pagination"]["count"] == 1
        assert len(body["data"]) == 1
        
        # Verify repository was called correctly
        mock_snapshot_repository.list_snapshots.assert_called_once_with(
            fund_id="test-fund-123",
            start_month="2024-01",
            end_month="2024-12",
            limit=10,
            last_key=None
        )

    def test_delete_fund_snapshot_success(self, mock_session_manager, mock_snapshot_repository):
        """Test successful fund snapshot deletion."""
        # Mock snapshot deletion
        mock_snapshot_repository.delete_snapshot.return_value = True
        
        # Create test event
        event = {
            "httpMethod": "DELETE",
            "path": "/funds/test-fund-123/snapshots/2024-12",
            "pathParameters": {"id": "test-fund-123", "month": "2024-12"},
            "headers": {"Authorization": "Bearer test-token"}
        }
        
        # Call the handler
        response = lambda_handler(event, {})
        
        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["success"] is True
        assert "deleted successfully" in body["message"].lower()
        
        # Verify repository was called correctly
        mock_snapshot_repository.delete_snapshot.assert_called_once_with("test-fund-123", "2024-12")

    def test_delete_fund_snapshot_not_found(self, mock_session_manager, mock_snapshot_repository):
        """Test fund snapshot deletion when snapshot doesn't exist."""
        # Mock snapshot not found
        mock_snapshot_repository.delete_snapshot.return_value = False
        
        # Create test event
        event = {
            "httpMethod": "DELETE",
            "path": "/funds/test-fund-123/snapshots/2024-12",
            "pathParameters": {"id": "test-fund-123", "month": "2024-12"},
            "headers": {"Authorization": "Bearer test-token"}
        }
        
        # Call the handler
        response = lambda_handler(event, {})
        
        # Verify response
        assert response["statusCode"] == 404
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "not found" in body["message"].lower()

    def test_create_snapshot_invalid_data(self, mock_session_manager, mock_fund_repository):
        """Test snapshot creation with invalid data."""
        # Mock fund exists
        mock_fund_repository.get_fund.return_value = Mock(id="test-fund-123")
        
        # Create test event with invalid data
        event = {
            "httpMethod": "POST",
            "path": "/funds/test-fund-123/snapshots/2024-13",  # Invalid month
            "pathParameters": {"id": "test-fund-123", "month": "2024-13"},
            "headers": {"Authorization": "Bearer test-token"},
            "body": json.dumps({
                "nav": "invalid-number",  # Invalid NAV
                "notes": "Test snapshot"
            })
        }
        
        # Call the handler
        response = lambda_handler(event, {})
        
        # Verify response
        assert response["statusCode"] == 400
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "validation" in body["message"].lower() or "invalid" in body["message"].lower()

    def test_unauthorized_access(self):
        """Test API access without valid authentication."""
        with patch('src.functions.api.funds.SessionManager') as mock:
            session_manager = Mock()
            session_manager.validate_session.return_value = {"valid": False}
            mock.return_value = session_manager
            
            # Create test event without valid auth
            event = {
                "httpMethod": "GET",
                "path": "/funds/test-fund-123/snapshots/2024-12",
                "pathParameters": {"id": "test-fund-123", "month": "2024-12"},
                "headers": {}
            }
            
            # Call the handler
            response = lambda_handler(event, {})
            
            # Verify response
            assert response["statusCode"] == 401
            body = json.loads(response["body"])
            assert body["success"] is False
            assert "unauthorized" in body["message"].lower() or "session" in body["message"].lower()

    def test_fund_not_found_for_snapshot_creation(self, mock_session_manager, mock_fund_repository):
        """Test snapshot creation when fund doesn't exist."""
        # Mock fund not found
        mock_fund_repository.get_fund.return_value = None
        
        # Create test event
        event = {
            "httpMethod": "POST",
            "path": "/funds/nonexistent-fund/snapshots/2024-12",
            "pathParameters": {"id": "nonexistent-fund", "month": "2024-12"},
            "headers": {"Authorization": "Bearer test-token"},
            "body": json.dumps({
                "nav": "150.75",
                "notes": "Test snapshot"
            })
        }
        
        # Call the handler
        response = lambda_handler(event, {})
        
        # Verify response
        assert response["statusCode"] == 404
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "fund" in body["message"].lower() and "not found" in body["message"].lower()


if __name__ == "__main__":
    pytest.main([__file__])
