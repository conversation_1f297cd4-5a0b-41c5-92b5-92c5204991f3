"""
Test suite for Fund Snapshot Repository functionality.
"""

import pytest
import os
from datetime import datetime, timezone
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock

# Set up test environment
os.environ["ENVIRONMENT"] = "test"
os.environ["FUND_SNAPSHOTS_TABLE"] = "test-fund-snapshots"

from src.shared.repositories.fund_snapshot_repository import FundSnapshotRepository
from src.shared.models.fund_snapshot import (
    FundSnapshot,
    FundSnapshotCreate,
    FundSnapshotUpdate,
)


class TestFundSnapshotRepository:
    """Test cases for FundSnapshotRepository."""

    @pytest.fixture
    def mock_dynamodb_table(self):
        """Mock DynamoDB table for testing."""
        table = Mock()
        table.put_item = Mock()
        table.get_item = Mock()
        table.query = Mock()
        table.delete_item = Mock()
        return table

    @pytest.fixture
    def repository(self, mock_dynamodb_table):
        """Create repository instance with mocked table."""
        repo = FundSnapshotRepository()
        repo.table = mock_dynamodb_table
        return repo

    @pytest.fixture
    def sample_snapshot_data(self):
        """Sample snapshot data for testing."""
        return FundSnapshotCreate(
            snapshot_month="2024-12",
            nav=Decimal("150.75"),
            total_assets=Decimal("1000000.00"),
            notes="Test snapshot data",
            market_data={
                "price": 150.75,
                "volume": 10000,
                "change": 2.5
            },
            holdings={
                "topHoldings": [
                    {
                        "name": "Apple Inc",
                        "symbol": "AAPL",
                        "percentage": 5.2,
                        "marketValue": 52000
                    }
                ],
                "sectorAllocation": {
                    "Technology": 25.5,
                    "Healthcare": 18.3
                }
            },
            performance_metrics={
                "totalReturn": 12.5,
                "annualizedReturn": 8.7,
                "volatility": 15.2
            }
        )

    def test_create_snapshot_success(self, repository, sample_snapshot_data, mock_dynamodb_table):
        """Test successful snapshot creation."""
        # Mock that no existing snapshot exists
        mock_dynamodb_table.get_item.return_value = {"Item": None}
        
        # Test snapshot creation
        result = repository.create_or_update_snapshot(
            fund_id="test-fund-123",
            snapshot_data=sample_snapshot_data,
            user_id="test-user"
        )
        
        # Verify the result
        assert result.fund_id == "test-fund-123"
        assert result.snapshot_month == "2024-12"
        assert result.nav == Decimal("150.75")
        assert result.created_by == "test-user"
        assert result.notes == "Test snapshot data"
        
        # Verify DynamoDB put_item was called
        mock_dynamodb_table.put_item.assert_called_once()

    def test_update_existing_snapshot(self, repository, sample_snapshot_data, mock_dynamodb_table):
        """Test updating an existing snapshot."""
        # Mock existing snapshot
        existing_snapshot = FundSnapshot(
            fund_id="test-fund-123",
            snapshot_month="2024-12",
            nav=Decimal("140.00"),
            created_at=datetime(2024, 12, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 12, 1, tzinfo=timezone.utc),
            created_by="original-user"
        )
        
        # Mock repository methods
        repository.get_snapshot = Mock(return_value=existing_snapshot)
        
        # Test snapshot update
        result = repository.create_or_update_snapshot(
            fund_id="test-fund-123",
            snapshot_data=sample_snapshot_data,
            user_id="test-user"
        )
        
        # Verify the result maintains original creation time but updates other fields
        assert result.fund_id == "test-fund-123"
        assert result.snapshot_month == "2024-12"
        assert result.nav == Decimal("150.75")  # Updated value
        assert result.created_by == "test-user"  # Updated user
        assert result.created_at == existing_snapshot.created_at  # Original creation time
        
        # Verify DynamoDB put_item was called
        mock_dynamodb_table.put_item.assert_called_once()

    def test_get_snapshot_success(self, repository, mock_dynamodb_table):
        """Test successful snapshot retrieval."""
        # Mock DynamoDB response
        mock_item = {
            "fund_id": "test-fund-123",
            "snapshot_month": "2024-12",
            "nav": Decimal("150.75"),
            "created_at": "2024-12-01T10:00:00+00:00",
            "updated_at": "2024-12-01T10:00:00+00:00",
            "created_by": "test-user"
        }
        mock_dynamodb_table.get_item.return_value = {"Item": mock_item}
        
        # Mock the base class get_by_id method
        repository.get_by_id = Mock(return_value=FundSnapshot(**mock_item))
        
        # Test snapshot retrieval
        result = repository.get_snapshot("test-fund-123", "2024-12")
        
        # Verify the result
        assert result is not None
        assert result.fund_id == "test-fund-123"
        assert result.snapshot_month == "2024-12"
        assert result.nav == Decimal("150.75")

    def test_get_snapshot_not_found(self, repository):
        """Test snapshot retrieval when snapshot doesn't exist."""
        # Mock the base class get_by_id method to return None
        repository.get_by_id = Mock(return_value=None)
        
        # Test snapshot retrieval
        result = repository.get_snapshot("test-fund-123", "2024-12")
        
        # Verify no result
        assert result is None

    def test_get_latest_snapshot(self, repository, mock_dynamodb_table):
        """Test getting the latest snapshot for a fund."""
        # Mock DynamoDB query response
        mock_items = [
            {
                "fund_id": "test-fund-123",
                "snapshot_month": "2024-12",
                "nav": Decimal("150.75"),
                "created_at": "2024-12-01T10:00:00+00:00",
                "updated_at": "2024-12-01T10:00:00+00:00",
                "created_by": "test-user"
            }
        ]
        mock_dynamodb_table.query.return_value = {"Items": mock_items}
        
        # Test latest snapshot retrieval
        result = repository.get_latest_snapshot("test-fund-123")
        
        # Verify the result
        assert result is not None
        assert result.fund_id == "test-fund-123"
        assert result.snapshot_month == "2024-12"
        
        # Verify query was called with correct parameters
        mock_dynamodb_table.query.assert_called_once()
        call_args = mock_dynamodb_table.query.call_args
        assert call_args[1]["ScanIndexForward"] is False  # Descending order
        assert call_args[1]["Limit"] == 1

    def test_list_snapshots_with_date_range(self, repository, mock_dynamodb_table):
        """Test listing snapshots with date range filtering."""
        # Mock DynamoDB query response
        mock_items = [
            {
                "fund_id": "test-fund-123",
                "snapshot_month": "2024-12",
                "nav": Decimal("150.75"),
                "created_at": "2024-12-01T10:00:00+00:00",
                "updated_at": "2024-12-01T10:00:00+00:00",
                "created_by": "test-user"
            },
            {
                "fund_id": "test-fund-123",
                "snapshot_month": "2024-11",
                "nav": Decimal("145.50"),
                "created_at": "2024-11-01T10:00:00+00:00",
                "updated_at": "2024-11-01T10:00:00+00:00",
                "created_by": "test-user"
            }
        ]
        mock_dynamodb_table.query.return_value = {"Items": mock_items}
        
        # Test listing snapshots with date range
        result = repository.list_snapshots(
            fund_id="test-fund-123",
            start_month="2024-11",
            end_month="2024-12",
            limit=10
        )
        
        # Verify the result
        assert result["count"] == 2
        assert len(result["snapshots"]) == 2
        assert result["snapshots"][0].snapshot_month == "2024-12"
        assert result["snapshots"][1].snapshot_month == "2024-11"

    def test_delete_snapshot_success(self, repository):
        """Test successful snapshot deletion."""
        # Mock the base class delete method
        repository.delete = Mock(return_value=True)
        
        # Test snapshot deletion
        result = repository.delete_snapshot("test-fund-123", "2024-12")
        
        # Verify the result
        assert result is True
        
        # Verify delete was called with correct primary key
        repository.delete.assert_called_once_with({
            "fund_id": "test-fund-123",
            "snapshot_month": "2024-12"
        })

    def test_snapshot_month_validation(self, sample_snapshot_data):
        """Test snapshot month validation."""
        # Test invalid month format
        with pytest.raises(ValueError, match="snapshot_month must be in YYYY-MM format"):
            FundSnapshotCreate(
                snapshot_month="2024-13",  # Invalid month
                nav=Decimal("150.75")
            )
        
        # Test future month
        future_month = "2030-12"
        with pytest.raises(ValueError, match="Cannot create snapshots for future months"):
            FundSnapshotCreate(
                snapshot_month=future_month,
                nav=Decimal("150.75")
            )

    def test_decimal_serialization(self, repository):
        """Test proper handling of Decimal fields."""
        snapshot = FundSnapshot(
            fund_id="test-fund-123",
            snapshot_month="2024-12",
            nav=Decimal("150.75"),
            total_assets=Decimal("1000000.50"),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            created_by="test-user"
        )
        
        # Test serialization to DynamoDB format
        item = repository._serialize_item(snapshot)
        
        # Verify Decimal fields are preserved
        assert isinstance(item["nav"], Decimal)
        assert isinstance(item["total_assets"], Decimal)
        assert item["nav"] == Decimal("150.75")
        assert item["total_assets"] == Decimal("1000000.50")


if __name__ == "__main__":
    pytest.main([__file__])
