"""
Fund Snapshot Repository for DynamoDB operations.
Provides CRUD operations for fund snapshot entities.
"""

import os
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from decimal import Decimal
from botocore.exceptions import ClientError

from aws_lambda_powertools import Logger, Tracer

from .base import DynamoDBRepository
from ..models.fund_snapshot import (
    FundSnapshot,
    FundSnapshotCreate,
    FundSnapshotUpdate,
    FundSnapshotDynamoDBItem,
)

logger = Logger()
tracer = Tracer()


class FundSnapshotRepository(DynamoDBRepository):
    """Repository for Fund Snapshot entities in DynamoDB."""

    def __init__(self, region: str = "us-east-1"):
        """Initialize the Fund Snapshot repository."""
        # Get table name from environment variable or construct it
        environment = os.getenv("ENVIRONMENT", "dev")
        table_name = (
            os.getenv("FUND_SNAPSHOTS_TABLE")
            or f"fundflow-{environment}-fund-snapshots"
        )
        super().__init__(table_name, region)
        self.logger = logger

    def _get_primary_key(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get the primary key for a fund snapshot item."""
        return {"fund_id": item["fund_id"], "snapshot_month": item["snapshot_month"]}

    def _serialize_item(self, snapshot: FundSnapshot) -> Dict[str, Any]:
        """Serialize FundSnapshot object to DynamoDB format."""
        return FundSnapshotDynamoDBItem.to_dynamodb_item(snapshot)

    def _deserialize_item(self, item: Dict[str, Any]) -> FundSnapshot:
        """Deserialize DynamoDB item to FundSnapshot object."""
        return FundSnapshotDynamoDBItem.from_dynamodb_item(item)

    @tracer.capture_method
    def create_or_update_snapshot(
        self, fund_id: str, snapshot_data: FundSnapshotCreate, user_id: str
    ) -> FundSnapshot:
        """Create new or update existing snapshot for a fund and month."""
        try:
            current_time = datetime.now(timezone.utc)

            # Check if snapshot already exists
            existing_snapshot = self.get_snapshot(fund_id, snapshot_data.snapshot_month)

            if existing_snapshot:
                # Update existing snapshot
                snapshot = FundSnapshot(
                    fund_id=fund_id,
                    snapshot_month=snapshot_data.snapshot_month,
                    market_data=snapshot_data.market_data,
                    holdings=snapshot_data.holdings,
                    performance_metrics=snapshot_data.performance_metrics,
                    risk_analytics=snapshot_data.risk_analytics,
                    nav=snapshot_data.nav,
                    total_assets=snapshot_data.total_assets,
                    created_at=existing_snapshot.created_at,  # Keep original creation time
                    updated_at=current_time,
                    created_by=user_id,
                    notes=snapshot_data.notes,
                    data_sources=snapshot_data.data_sources,
                )

                self.logger.info(
                    f"Updating existing snapshot for fund {fund_id}, month {snapshot_data.snapshot_month}"
                )
            else:
                # Create new snapshot
                snapshot = FundSnapshot(
                    fund_id=fund_id,
                    snapshot_month=snapshot_data.snapshot_month,
                    market_data=snapshot_data.market_data,
                    holdings=snapshot_data.holdings,
                    performance_metrics=snapshot_data.performance_metrics,
                    risk_analytics=snapshot_data.risk_analytics,
                    nav=snapshot_data.nav,
                    total_assets=snapshot_data.total_assets,
                    created_at=current_time,
                    updated_at=current_time,
                    created_by=user_id,
                    notes=snapshot_data.notes,
                    data_sources=snapshot_data.data_sources,
                )

                self.logger.info(
                    f"Creating new snapshot for fund {fund_id}, month {snapshot_data.snapshot_month}"
                )

            # Save to DynamoDB
            item = self._serialize_item(snapshot)
            self.table.put_item(Item=item)

            self.logger.info(
                f"Snapshot saved successfully",
                extra={
                    "fund_id": fund_id,
                    "snapshot_month": snapshot_data.snapshot_month,
                    "user_id": user_id,
                },
            )
            return snapshot

        except Exception as e:
            self.logger.error(
                f"Error creating/updating snapshot",
                extra={
                    "error": str(e),
                    "fund_id": fund_id,
                    "snapshot_month": snapshot_data.snapshot_month,
                },
            )
            raise

    @tracer.capture_method
    def get_snapshot(self, fund_id: str, snapshot_month: str) -> Optional[FundSnapshot]:
        """Get snapshot for specific fund and month."""
        try:
            primary_key = {"fund_id": fund_id, "snapshot_month": snapshot_month}
            return self.get_by_id(primary_key)

        except Exception as e:
            self.logger.error(
                f"Error retrieving snapshot",
                extra={
                    "error": str(e),
                    "fund_id": fund_id,
                    "snapshot_month": snapshot_month,
                },
            )
            raise

    @tracer.capture_method
    def get_latest_snapshot(self, fund_id: str) -> Optional[FundSnapshot]:
        """Get most recent snapshot for a fund."""
        try:
            response = self.table.query(
                KeyConditionExpression="fund_id = :fund_id",
                ExpressionAttributeValues={":fund_id": fund_id},
                ScanIndexForward=False,  # Sort in descending order
                Limit=1,
            )

            items = response.get("Items", [])
            if items:
                return self._deserialize_item(items[0])

            self.logger.info(f"No snapshots found for fund {fund_id}")
            return None

        except Exception as e:
            self.logger.error(
                f"Error retrieving latest snapshot",
                extra={"error": str(e), "fund_id": fund_id},
            )
            raise

    @tracer.capture_method
    def list_snapshots(
        self,
        fund_id: str,
        start_month: Optional[str] = None,
        end_month: Optional[str] = None,
        limit: Optional[int] = None,
        last_key: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """List all snapshots for a fund with optional date range filtering."""
        try:
            query_kwargs = {
                "KeyConditionExpression": "fund_id = :fund_id",
                "ExpressionAttributeValues": {":fund_id": fund_id},
                "ScanIndexForward": False,  # Most recent first
            }

            # Add date range filtering
            if start_month or end_month:
                if start_month and end_month:
                    query_kwargs[
                        "KeyConditionExpression"
                    ] += " AND snapshot_month BETWEEN :start_month AND :end_month"
                    query_kwargs["ExpressionAttributeValues"][
                        ":start_month"
                    ] = start_month
                    query_kwargs["ExpressionAttributeValues"][":end_month"] = end_month
                elif start_month:
                    query_kwargs[
                        "KeyConditionExpression"
                    ] += " AND snapshot_month >= :start_month"
                    query_kwargs["ExpressionAttributeValues"][
                        ":start_month"
                    ] = start_month
                elif end_month:
                    query_kwargs[
                        "KeyConditionExpression"
                    ] += " AND snapshot_month <= :end_month"
                    query_kwargs["ExpressionAttributeValues"][":end_month"] = end_month

            if limit:
                query_kwargs["Limit"] = limit

            if last_key:
                query_kwargs["ExclusiveStartKey"] = last_key

            response = self.table.query(**query_kwargs)

            items = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "snapshots": items,
                "count": len(items),
            }

            if "LastEvaluatedKey" in response:
                result["last_evaluated_key"] = response["LastEvaluatedKey"]

            self.logger.info(
                f"Listed snapshots for fund {fund_id}",
                extra={"count": len(items), "fund_id": fund_id},
            )
            return result

        except Exception as e:
            self.logger.error(
                f"Error listing snapshots",
                extra={"error": str(e), "fund_id": fund_id},
            )
            raise

    @tracer.capture_method
    def delete_snapshot(self, fund_id: str, snapshot_month: str) -> bool:
        """Delete a specific snapshot."""
        try:
            primary_key = {"fund_id": fund_id, "snapshot_month": snapshot_month}
            return self.delete(primary_key)

        except Exception as e:
            self.logger.error(
                f"Error deleting snapshot",
                extra={
                    "error": str(e),
                    "fund_id": fund_id,
                    "snapshot_month": snapshot_month,
                },
            )
            raise

    @tracer.capture_method
    def get_snapshots_by_created_date(
        self,
        fund_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None,
    ) -> List[FundSnapshot]:
        """Query snapshots by creation date using LSI."""
        try:
            query_kwargs = {
                "IndexName": "fund_created_at_index",
                "KeyConditionExpression": "fund_id = :fund_id",
                "ExpressionAttributeValues": {":fund_id": fund_id},
                "ScanIndexForward": False,  # Most recent first
            }

            # Add date range filtering
            if start_date or end_date:
                if start_date and end_date:
                    query_kwargs[
                        "KeyConditionExpression"
                    ] += " AND created_at BETWEEN :start_date AND :end_date"
                    query_kwargs["ExpressionAttributeValues"][
                        ":start_date"
                    ] = start_date.isoformat()
                    query_kwargs["ExpressionAttributeValues"][
                        ":end_date"
                    ] = end_date.isoformat()
                elif start_date:
                    query_kwargs[
                        "KeyConditionExpression"
                    ] += " AND created_at >= :start_date"
                    query_kwargs["ExpressionAttributeValues"][
                        ":start_date"
                    ] = start_date.isoformat()
                elif end_date:
                    query_kwargs[
                        "KeyConditionExpression"
                    ] += " AND created_at <= :end_date"
                    query_kwargs["ExpressionAttributeValues"][
                        ":end_date"
                    ] = end_date.isoformat()

            if limit:
                query_kwargs["Limit"] = limit

            response = self.table.query(**query_kwargs)

            items = [self._deserialize_item(item) for item in response.get("Items", [])]

            self.logger.info(
                f"Queried snapshots by created date for fund {fund_id}",
                extra={"count": len(items), "fund_id": fund_id},
            )
            return items

        except Exception as e:
            self.logger.error(
                f"Error querying snapshots by created date",
                extra={"error": str(e), "fund_id": fund_id},
            )
            raise

    @tracer.capture_method
    def batch_get_latest_snapshots(
        self, fund_ids: List[str]
    ) -> Dict[str, FundSnapshot]:
        """Get latest snapshots for multiple funds."""
        try:
            result = {}

            # Process each fund individually since we need the latest snapshot
            for fund_id in fund_ids:
                latest_snapshot = self.get_latest_snapshot(fund_id)
                if latest_snapshot:
                    result[fund_id] = latest_snapshot

            self.logger.info(
                f"Retrieved latest snapshots for {len(result)} out of {len(fund_ids)} funds"
            )
            return result

        except Exception as e:
            self.logger.error(
                f"Error batch retrieving latest snapshots",
                extra={"error": str(e), "fund_count": len(fund_ids)},
            )
            raise

    @tracer.capture_method
    def update_snapshot(
        self,
        fund_id: str,
        snapshot_month: str,
        update_data: FundSnapshotUpdate,
        user_id: str,
    ) -> Optional[FundSnapshot]:
        """Update an existing snapshot."""
        try:
            # Get existing snapshot
            existing_snapshot = self.get_snapshot(fund_id, snapshot_month)
            if not existing_snapshot:
                self.logger.warning(
                    f"Snapshot not found for update",
                    extra={"fund_id": fund_id, "snapshot_month": snapshot_month},
                )
                return None

            # Create updated snapshot
            update_dict = update_data.model_dump(exclude_unset=True)
            snapshot_dict = existing_snapshot.model_dump()

            # Update only provided fields
            for key, value in update_dict.items():
                if value is not None:
                    snapshot_dict[key] = value

            # Update metadata
            snapshot_dict["updated_at"] = datetime.now(timezone.utc)
            snapshot_dict["created_by"] = user_id

            updated_snapshot = FundSnapshot(**snapshot_dict)

            # Save to DynamoDB
            item = self._serialize_item(updated_snapshot)
            self.table.put_item(Item=item)

            self.logger.info(
                f"Snapshot updated successfully",
                extra={
                    "fund_id": fund_id,
                    "snapshot_month": snapshot_month,
                    "user_id": user_id,
                },
            )
            return updated_snapshot

        except Exception as e:
            self.logger.error(
                f"Error updating snapshot",
                extra={
                    "error": str(e),
                    "fund_id": fund_id,
                    "snapshot_month": snapshot_month,
                },
            )
            raise
