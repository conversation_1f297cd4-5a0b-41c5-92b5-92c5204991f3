"""
Database connection manager for DynamoDB repositories.
Provides centralized configuration and initialization of repositories.
"""

import os
from typing import Optional

from .repositories import (
    FundRepository,
    UserRepository,
    ReportRepository,
    TokenRepository,
    PortfolioRepository,
)
from .repositories.fund_snapshot_repository import FundSnapshotRepository


class DatabaseManager:
    """Manages database connections and repository instances."""

    def __init__(self, region: Optional[str] = None):
        """
        Initialize database manager.

        Args:
            region: AWS region for DynamoDB operations
        """
        self.region = region or os.getenv("AWS_REGION", "us-east-1")
        self._fund_repository = None
        self._user_repository = None
        self._report_repository = None
        self._token_repository = None
        self._portfolio_repository = None
        self._fund_snapshot_repository = None

    @property
    def fund_repository(self) -> FundRepository:
        """Get Fund repository instance (lazy-loaded)."""
        if self._fund_repository is None:
            self._fund_repository = FundRepository(self.region)
        return self._fund_repository

    @property
    def user_repository(self) -> UserRepository:
        """Get User repository instance (lazy-loaded)."""
        if self._user_repository is None:
            self._user_repository = UserRepository(self.region)
        return self._user_repository

    @property
    def report_repository(self) -> ReportRepository:
        """Get Report repository instance (lazy-loaded)."""
        if self._report_repository is None:
            self._report_repository = ReportRepository(self.region)
        return self._report_repository

    @property
    def token_repository(self) -> TokenRepository:
        """Get Token repository instance (lazy-loaded)."""
        if self._token_repository is None:
            self._token_repository = TokenRepository(self.region)
        return self._token_repository

    @property
    def portfolio_repository(self) -> PortfolioRepository:
        """Get Portfolio repository instance (lazy-loaded)."""
        if self._portfolio_repository is None:
            self._portfolio_repository = PortfolioRepository(self.region)
        return self._portfolio_repository

    @property
    def fund_snapshot_repository(self) -> FundSnapshotRepository:
        """Get Fund Snapshot repository instance (lazy-loaded)."""
        if self._fund_snapshot_repository is None:
            self._fund_snapshot_repository = FundSnapshotRepository(self.region)
        return self._fund_snapshot_repository

    def health_check(self) -> dict:
        """
        Perform health check on database connections.

        Returns:
            Dict containing health status for each repository
        """
        health_status = {
            "database": "healthy",
            "region": self.region,
            "repositories": {},
        }

        try:
            # Test fund repository
            self.fund_repository.table.load()
            health_status["repositories"]["funds"] = "healthy"
        except Exception as e:
            health_status["repositories"]["funds"] = f"error: {str(e)}"
            health_status["database"] = "degraded"

        try:
            # Test user repository
            self.user_repository.table.load()
            health_status["repositories"]["users"] = "healthy"
        except Exception as e:
            health_status["repositories"]["users"] = f"error: {str(e)}"
            health_status["database"] = "degraded"

        try:
            # Test report repository
            self.report_repository.table.load()
            health_status["repositories"]["reports"] = "healthy"
        except Exception as e:
            health_status["repositories"]["reports"] = f"error: {str(e)}"
            health_status["database"] = "degraded"

        try:
            # Test token repository
            self.token_repository.table.load()
            health_status["repositories"]["tokens"] = "healthy"
        except Exception as e:
            health_status["repositories"]["tokens"] = f"error: {str(e)}"
            health_status["database"] = "degraded"

        try:
            # Test portfolio repository
            self.portfolio_repository.table.load()
            health_status["repositories"]["portfolios"] = "healthy"
        except Exception as e:
            health_status["repositories"]["portfolios"] = f"error: {str(e)}"
            health_status["database"] = "degraded"

        return health_status


# Global database manager instance
db = DatabaseManager()


def get_database() -> DatabaseManager:
    """
    Get the global database manager instance.

    Returns:
        DatabaseManager instance
    """
    return db


def get_fund_repository() -> FundRepository:
    """
    Get Fund repository instance.

    Returns:
        FundRepository instance
    """
    return db.fund_repository


def get_user_repository() -> UserRepository:
    """
    Get User repository instance.

    Returns:
        UserRepository instance
    """
    return db.user_repository


def get_report_repository() -> ReportRepository:
    """
    Get Report repository instance.

    Returns:
        ReportRepository instance
    """
    return db.report_repository


def get_token_repository() -> TokenRepository:
    """
    Get Token repository instance.

    Returns:
        TokenRepository instance
    """
    return db.token_repository


def get_portfolio_repository() -> PortfolioRepository:
    """
    Get Portfolio repository instance.

    Returns:
        PortfolioRepository instance
    """
    return db.portfolio_repository


def get_fund_snapshot_repository() -> FundSnapshotRepository:
    """
    Get Fund Snapshot repository instance.

    Returns:
        FundSnapshotRepository instance
    """
    return db.fund_snapshot_repository
