"""
Fund Snapshot data model for DynamoDB operations.
Defines the structure and validation for monthly fund snapshot entities.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from decimal import Decimal
import re

from pydantic import (
    BaseModel,
    field_serializer,
    Field,
    field_validator,
    model_validator,
    ConfigDict,
)
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel

# Import related models
from .fund import PerformanceMetrics, Holdings
from .market_data import (
    PriceData,
    ValuationMetrics,
    TechnicalIndicators,
    RiskAnalytics,
)


class FundSnapshot(PowertoolsBaseModel):
    """
    Fund snapshot data model for DynamoDB.

    DynamoDB Table: fundflow-{env}-fund-snapshots
    Primary Key: fund_id (HASH), snapshot_month (RANGE)
    Local Secondary Index: fund_created_at_index - fund_id (HASH), created_at (RANGE)
    """

    # Primary Key
    fund_id: str = Field(..., description="Fund identifier")
    snapshot_month: str = Field(..., description="Snapshot month in YYYY-MM format")

    # Snapshot Data
    market_data: Optional[Dict[str, Any]] = Field(
        None, description="Complete market data snapshot"
    )
    holdings: Optional[Holdings] = Field(
        None, description="Holdings information for that month"
    )
    performance_metrics: Optional[PerformanceMetrics] = Field(
        None, description="KPIs and performance data"
    )
    risk_analytics: Optional[RiskAnalytics] = Field(
        None, description="Risk metrics for that month"
    )

    # Key Financial Data
    nav: Optional[Decimal] = Field(None, gt=0, description="NAV for the month")
    total_assets: Optional[Decimal] = Field(
        None, ge=0, description="Total assets for the month"
    )

    # Metadata
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Creation timestamp",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp",
    )
    created_by: str = Field(..., description="User who created/updated the snapshot")
    notes: Optional[str] = Field(
        None, max_length=1000, description="Optional notes about the snapshot"
    )
    data_sources: Optional[Dict[str, str]] = Field(
        None, description="Data sources metadata"
    )

    model_config = ConfigDict(
        use_enum_values=True,
        populate_by_name=True,
        validate_assignment=True,
        extra="forbid",
    )

    @field_validator("snapshot_month")
    @classmethod
    def validate_snapshot_month(cls, v: str) -> str:
        """Validate snapshot month format (YYYY-MM)."""
        if not re.match(r"^\d{4}-\d{2}$", v):
            raise ValueError("snapshot_month must be in YYYY-MM format")

        # Parse to validate it's a valid date
        try:
            year, month = v.split("-")
            year_int, month_int = int(year), int(month)
            if not (1 <= month_int <= 12):
                raise ValueError("Month must be between 01 and 12")
            if year_int < 1900 or year_int > 2100:
                raise ValueError("Year must be between 1900 and 2100")
        except ValueError as e:
            raise ValueError(f"Invalid snapshot_month format: {e}")

        # Prevent future dates (allow current month)
        current_date = datetime.now(timezone.utc)
        current_month = current_date.strftime("%Y-%m")
        if v > current_month:
            raise ValueError("Cannot create snapshots for future months")

        return v

    @field_serializer("nav", "total_assets", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON."""
        return str(value) if value is not None else None

    @field_serializer("created_at", "updated_at", when_used="json")
    def serialize_datetime(self, value: datetime) -> str:
        """Serialize datetime to ISO format."""
        return value.isoformat()

    @model_validator(mode="before")
    @classmethod
    def convert_decimal_strings(cls, values):
        """Convert string values to Decimal for nav and total_assets."""
        if isinstance(values, dict):
            for field in ["nav", "total_assets"]:
                if field in values and values[field] is not None:
                    if isinstance(values[field], str):
                        try:
                            values[field] = Decimal(values[field])
                        except:
                            pass  # Let field validation handle the error
        return values


class FundSnapshotCreate(BaseModel):
    """Model for creating fund snapshots."""

    snapshot_month: str = Field(..., description="Snapshot month in YYYY-MM format")
    market_data: Optional[Dict[str, Any]] = None
    holdings: Optional[Holdings] = None
    performance_metrics: Optional[PerformanceMetrics] = None
    risk_analytics: Optional[RiskAnalytics] = None
    nav: Optional[Decimal] = Field(None, gt=0)
    total_assets: Optional[Decimal] = Field(None, ge=0)
    notes: Optional[str] = Field(None, max_length=1000)
    data_sources: Optional[Dict[str, str]] = None

    @field_validator("snapshot_month")
    @classmethod
    def validate_snapshot_month(cls, v: str) -> str:
        """Validate snapshot month format (YYYY-MM)."""
        if not re.match(r"^\d{4}-\d{2}$", v):
            raise ValueError("snapshot_month must be in YYYY-MM format")

        # Parse to validate it's a valid date
        try:
            year, month = v.split("-")
            year_int, month_int = int(year), int(month)
            if not (1 <= month_int <= 12):
                raise ValueError("Month must be between 01 and 12")
            if year_int < 1900 or year_int > 2100:
                raise ValueError("Year must be between 1900 and 2100")
        except ValueError as e:
            raise ValueError(f"Invalid snapshot_month format: {e}")

        # Prevent future dates (allow current month)
        current_date = datetime.now(timezone.utc)
        current_month = current_date.strftime("%Y-%m")
        if v > current_month:
            raise ValueError("Cannot create snapshots for future months")

        return v

    @model_validator(mode="before")
    @classmethod
    def convert_decimal_strings(cls, values):
        """Convert string values to Decimal for nav and total_assets."""
        if isinstance(values, dict):
            for field in ["nav", "total_assets"]:
                if field in values and values[field] is not None:
                    if isinstance(values[field], str):
                        try:
                            values[field] = Decimal(values[field])
                        except:
                            pass  # Let field validation handle the error
        return values


class FundSnapshotUpdate(BaseModel):
    """Model for updating fund snapshots."""

    market_data: Optional[Dict[str, Any]] = None
    holdings: Optional[Holdings] = None
    performance_metrics: Optional[PerformanceMetrics] = None
    risk_analytics: Optional[RiskAnalytics] = None
    nav: Optional[Decimal] = Field(None, gt=0)
    total_assets: Optional[Decimal] = Field(None, ge=0)
    notes: Optional[str] = Field(None, max_length=1000)
    data_sources: Optional[Dict[str, str]] = None

    @model_validator(mode="before")
    @classmethod
    def convert_decimal_strings(cls, values):
        """Convert string values to Decimal for nav and total_assets."""
        if isinstance(values, dict):
            for field in ["nav", "total_assets"]:
                if field in values and values[field] is not None:
                    if isinstance(values[field], str):
                        try:
                            values[field] = Decimal(values[field])
                        except:
                            pass  # Let field validation handle the error
        return values


class FundSnapshotResponse(FundSnapshot):
    """Fund snapshot response model for API responses."""

    pass


class FundSnapshotListParams(BaseModel):
    """Parameters for listing fund snapshots."""

    start_month: Optional[str] = Field(None, description="Start month filter (YYYY-MM)")
    end_month: Optional[str] = Field(None, description="End month filter (YYYY-MM)")
    limit: Optional[int] = Field(
        50, ge=1, le=100, description="Maximum number of results"
    )
    last_key: Optional[str] = Field(None, description="Pagination key")

    @field_validator("start_month", "end_month")
    @classmethod
    def validate_month_format(cls, v: Optional[str]) -> Optional[str]:
        """Validate month format (YYYY-MM)."""
        if v is None:
            return v

        if not re.match(r"^\d{4}-\d{2}$", v):
            raise ValueError("Month must be in YYYY-MM format")

        # Parse to validate it's a valid date
        try:
            year, month = v.split("-")
            year_int, month_int = int(year), int(month)
            if not (1 <= month_int <= 12):
                raise ValueError("Month must be between 01 and 12")
            if year_int < 1900 or year_int > 2100:
                raise ValueError("Year must be between 1900 and 2100")
        except ValueError as e:
            raise ValueError(f"Invalid month format: {e}")

        return v

    @model_validator(mode="after")
    def validate_date_range(self):
        """Validate that start_month is before end_month."""
        if self.start_month and self.end_month:
            if self.start_month > self.end_month:
                raise ValueError("start_month must be before or equal to end_month")
        return self


# DynamoDB specific utilities
class FundSnapshotDynamoDBItem:
    """Utility class for DynamoDB item transformations."""

    @staticmethod
    def to_dynamodb_item(snapshot: FundSnapshot) -> Dict[str, Any]:
        """Convert FundSnapshot model to DynamoDB item format."""
        item = snapshot.model_dump(by_alias=True)

        # Convert Decimal fields to DynamoDB Decimal format
        def convert_values(obj, parent_key=None):
            if isinstance(obj, dict):
                return {k: convert_values(v, k) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_values(item) for item in obj]
            elif isinstance(obj, Decimal):
                return obj
            elif isinstance(obj, str) and parent_key in ["nav", "total_assets"]:
                try:
                    return Decimal(obj)
                except:
                    return obj
            elif isinstance(obj, (int, float)) and parent_key in [
                "nav",
                "total_assets",
            ]:
                try:
                    return Decimal(str(obj))
                except:
                    return obj
            return obj

        converted_item = convert_values(item)

        # Ensure converted_item is a dictionary with string keys
        if not isinstance(converted_item, dict):
            raise ValueError("Invalid DynamoDB item format: expected dictionary")

        # Convert datetime objects to ISO strings for DynamoDB
        for key in ["created_at", "updated_at"]:
            if key in converted_item and isinstance(converted_item[key], datetime):
                converted_item[key] = converted_item[key].isoformat()

        return converted_item

    @staticmethod
    def from_dynamodb_item(item: Dict[str, Any]) -> FundSnapshot:
        """Convert DynamoDB item to FundSnapshot model."""
        # Create a copy to avoid modifying the original
        converted_item = dict(item)

        # Convert datetime strings back to datetime objects
        for key in ["created_at", "updated_at"]:
            if key in converted_item and isinstance(converted_item[key], str):
                try:
                    converted_item[key] = datetime.fromisoformat(
                        converted_item[key].replace("Z", "+00:00")
                    )
                except:
                    # If parsing fails, use current time as fallback
                    converted_item[key] = datetime.now(timezone.utc)

        # Convert Decimal fields
        for key in ["nav", "total_assets"]:
            if key in converted_item and converted_item[key] is not None:
                if isinstance(converted_item[key], (str, int, float)):
                    try:
                        converted_item[key] = Decimal(str(converted_item[key]))
                    except:
                        converted_item[key] = None

        return FundSnapshot(**converted_item)
