"""
Fund Management API endpoints.
Provides CRUD operations for fund entities with JWT authentication.
"""

import json
import random
import time
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

from aws_lambda_powertools import Logger, Tracer, Metrics
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.metrics import MetricUnit
from pydantic import ValidationError

from shared.models.requests import (
    FundCreateRequest,
    FundUpdateRequest,
    FundQueryRequest,
)
from shared.api.responses import APIResponse, RequestValidator, CORSHandler
from shared.models.fund import Fund, FundResponse, PerformanceMetrics
from shared.models.market_data import (
    MarketDataInput,
    PriceData,
    ValuationMetrics,
    TechnicalIndicators,
    RiskAnalytics,
    MarketDataSummary,
)

from shared.validation import FundValidationService
from shared.utils.validation_response import ValidationResponseHandler
from shared.database import get_fund_repository, get_fund_snapshot_repository
from shared.repositories.historical_price_repository import (
    get_historical_price_repository,
)
from shared.models.fund_snapshot import (
    FundSnapshotCreate,
    FundSnapshotUpdate,
    FundSnapshotListParams,
)
from shared.api.auth_dependencies import create_user_context
from shared.security.session_manager import SessionManager
from shared.utils.s3_photo_manager import photo_manager

# Initialize AWS Lambda Powertools
logger = Logger()
tracer = Tracer()
metrics = Metrics()

# No need to rebuild models since we're not using FundResponse with forward references


def convert_analytics_to_performance_metrics(
    analytics_data: Dict[str, Any],
    existing_performance: Optional[PerformanceMetrics] = None,
) -> PerformanceMetrics:
    """
    Convert frontend analytics data structure to PerformanceMetrics model.

    Args:
        analytics_data: Frontend analytics data with nested structure
        existing_performance: Existing performance metrics to update (optional)

    Returns:
        PerformanceMetrics object with converted data
    """
    from decimal import Decimal

    # Start with existing performance metrics or create new one
    if existing_performance:
        # Convert to dict and update with new data
        performance_dict = existing_performance.model_dump()
    else:
        performance_dict = {}

    # Extract KPIs data
    kpis = analytics_data.get("kpis", {})
    if kpis:
        # Map KPI fields to performance metrics fields
        kpi_mappings = {
            "totalReturn": "one_year_return",
            "annualizedReturn": "one_year_return",
            "volatility": "volatility",
            "sharpeRatio": "sharpe_ratio",
            "alpha": "alpha",
            "beta": "beta",
            "maxDrawdown": "max_drawdown",
            "informationRatio": "information_ratio",
            "sortinoRatio": "sortino_ratio",
            "calmarRatio": "calmar_ratio",
            "treynorRatio": "treynor_ratio",
            "trackingError": "tracking_error",
        }

        for frontend_field, backend_field in kpi_mappings.items():
            if frontend_field in kpis and kpis[frontend_field] is not None:
                # Convert to Decimal
                performance_dict[backend_field] = Decimal(str(kpis[frontend_field]))

    # Extract risk metrics data
    risk_metrics = analytics_data.get("riskMetrics", {})
    if risk_metrics:
        # Map risk metrics fields to performance metrics fields
        risk_mappings = {
            "standardDeviation": "volatility",
            "downSideRisk": "downside_deviation",
            "downsideDeviation": "downside_deviation",
            "varRisk": "var_1d_95",
            "var1d95": "var_1d_95",
            "var1d99": "var_1d_99",
            "sortRatio": "sortino_ratio",
            "calmarRatio": "calmar_ratio",
            "correlation": "correlation",
        }

        for frontend_field, backend_field in risk_mappings.items():
            if (
                frontend_field in risk_metrics
                and risk_metrics[frontend_field] is not None
            ):
                # Convert to Decimal
                performance_dict[backend_field] = Decimal(
                    str(risk_metrics[frontend_field])
                )

    # Create and return PerformanceMetrics object
    return PerformanceMetrics(**performance_dict)


@logger.inject_lambda_context(log_event=True)
@tracer.capture_lambda_handler
@metrics.log_metrics
def handler(event: Dict[str, Any], context: LambdaContext) -> Dict[str, Any]:
    """
    Lambda handler for fund management operations.

    Args:
        event: API Gateway event
        context: Lambda context

    Returns:
        API Gateway response
    """
    # Handle CORS preflight requests
    if CORSHandler.is_options_request(event):
        return CORSHandler.handle_options()

    try:
        # Extract request info
        method = event.get("httpMethod", "")
        path = event.get("path", "")

        logger.info(f"Processing {method} request to {path}")

        # Route requests based on method and path
        if method == "GET":
            if path.endswith("/funds") or "/funds?" in path:
                # GET /funds - List funds with optional filters
                return handle_list_funds(event)
            elif "/details" in path:
                # GET /funds/{fund_id}/details - Get enriched fund details
                return handle_get_fund_details(event)
            elif "/historical" in path:
                # GET /funds/{fund_id}/historical - Get historical data
                return handle_get_fund_historical(event)
            elif "/market-data" in path:
                # GET /funds/{fund_id}/market-data - Get market data
                return handle_get_market_data(event)
            elif "/snapshots/" in path and path.count("/") >= 4:
                # GET /funds/{fund_id}/snapshots/{month} - Get specific snapshot
                return handle_get_fund_snapshot(event)
            elif "/snapshots" in path:
                # GET /funds/{fund_id}/snapshots - List fund snapshots
                return handle_list_fund_snapshots(event)
            elif "/funds/" in path:
                # GET /funds/{fund_id} - Get specific fund
                return handle_get_fund(event)
            else:
                logger.warning(f"Unsupported GET path: {path}")
                return APIResponse.not_found("Endpoint not found")

        elif method == "POST":
            if path.endswith("/funds"):
                # POST /funds - Create new fund
                return handle_create_fund(event)
            elif path.endswith("/funds/bulk-update"):
                # POST /funds/bulk-update - Bulk update funds
                return handle_bulk_update_funds(event)
            elif "/market-data" in path:
                # POST /funds/{fund_id}/market-data - Submit market data input
                return handle_market_data_input(event)
            elif "/manager-photo" in path:
                # POST /funds/{fund_id}/manager-photo - Upload fund manager photo
                return handle_upload_fund_manager_photo(event, context)
            elif "/snapshots/" in path:
                # POST /funds/{fund_id}/snapshots/{month} - Create/update snapshot
                return handle_create_fund_snapshot(event)
            else:
                logger.warning(f"Unsupported POST path: {path}")
                return APIResponse.not_found("Endpoint not found")

        elif method == "PUT":
            if "/funds/" in path:
                # PUT /funds/{fund_id} - Update fund
                return handle_update_fund(event)
            else:
                logger.warning(f"Unsupported PUT path: {path}")
                return APIResponse.not_found("Endpoint not found")

        elif method == "DELETE":
            if "/manager-photo" in path:
                # DELETE /funds/{fund_id}/manager-photo - Delete fund manager photo
                return handle_delete_fund_manager_photo(event, context)
            elif "/snapshots/" in path:
                # DELETE /funds/{fund_id}/snapshots/{month} - Delete snapshot
                return handle_delete_fund_snapshot(event)
            elif "/funds/" in path:
                # DELETE /funds/{fund_id} - Delete fund
                return handle_delete_fund(event)
            else:
                logger.warning(f"Unsupported DELETE path: {path}")
                return APIResponse.not_found("Endpoint not found")

        else:
            logger.warning(f"Unsupported HTTP method: {method}")
            return APIResponse.error(
                "METHOD_NOT_ALLOWED", f"Method {method} not allowed", 405
            )

    except Exception as e:
        logger.exception("Error in fund management handler")
        metrics.add_metric(name="FundAPIErrors", unit=MetricUnit.Count, value=1)
        # Create simple error response to avoid logging conflicts
        return {
            "statusCode": 500,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*",
            },
            "body": json.dumps(
                {"error": "INTERNAL_SERVER_ERROR", "message": "Internal server error"}
            ),
        }


@tracer.capture_method
def handle_list_funds(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds - List funds with optional filters and pagination."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        logger.info(
            "Processing list funds request",
            extra={"user_id": user_context.get("user_id")},
        )

        # Parse query parameters
        query_params = event.get("queryStringParameters") or {}

        # Create query request from parameters
        try:
            query_request = FundQueryRequest(**query_params)
        except ValidationError as e:
            logger.warning(f"Invalid query parameters: {e}")
            return APIResponse.validation_error(
                "Invalid query parameters", [dict(error) for error in e.errors()]
            )

        # Get fund repository
        fund_repo = get_fund_repository()

        # Build filters for the query
        funds_result = fund_repo.list_funds(
            limit=query_request.page_size,
            status=query_request.status.value if query_request.status else None,
            fund_type=(query_request.fund_type if query_request.fund_type else None),
            search=query_request.search,
        )

        # Convert funds to response format (skip FundResponse to avoid model rebuild issues)
        fund_responses = funds_result[
            "funds"
        ]  # Repository returns "funds" key, not "items"

        response_data = {
            "funds": [fund.model_dump(by_alias=True) for fund in fund_responses],
            "pagination": {
                "page": query_request.page,
                "page_size": query_request.page_size,
                "total_count": funds_result["count"],
                "has_more": funds_result.get("has_more", False),
            },
            "filters_applied": {
                "status": query_request.status.value if query_request.status else None,
                "fund_type": (
                    query_request.fund_type if query_request.fund_type else None
                ),
                "search": query_request.search,
            },
        }

        metrics.add_metric(
            name="FundsListed", unit=MetricUnit.Count, value=len(fund_responses)
        )
        logger.info(f"Successfully listed {len(fund_responses)} funds")

        return APIResponse.success(
            message=f"Retrieved {len(fund_responses)} funds", data=response_data
        )

    except Exception as e:
        logger.exception("Error in list funds handler")
        metrics.add_metric(name="ListFundsErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to retrieve funds")


@tracer.capture_method
def handle_get_fund(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds/{fund_id} - Get specific fund."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id from path
        fund_id = extract_fund_id_from_path(event.get("path", ""))
        if not fund_id:
            return APIResponse.validation_error("Fund ID is required")

        logger.info(
            "Processing get fund request",
            extra={"user_id": user_context.get("user_id"), "fund_id": fund_id},
        )

        # Get fund repository and retrieve fund
        fund_repo = get_fund_repository()
        fund = fund_repo.get_by_fund_id(fund_id)

        if not fund:
            logger.warning(f"Fund not found: {fund_id}")
            return APIResponse.not_found(f"Fund {fund_id} not found")

        # Skip FundResponse to avoid model rebuild issues
        fund_response = fund

        metrics.add_metric(name="FundRetrieved", unit=MetricUnit.Count, value=1)
        logger.info(f"Successfully retrieved fund: {fund_id}")

        return APIResponse.success(
            message="Fund retrieved successfully",
            data=fund_response.model_dump(by_alias=True),
        )

    except Exception as e:
        logger.exception("Error in get fund handler")
        metrics.add_metric(name="GetFundErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to retrieve fund")


@tracer.capture_method
def handle_get_fund_details(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds/{fund_id}/details - Get enriched fund details with analytics."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id from path
        fund_id = extract_fund_id_from_path(event.get("path", ""))
        if not fund_id:
            return APIResponse.validation_error("Fund ID is required")

        logger.info(
            "Processing get fund details request",
            extra={"user_id": user_context.get("user_id"), "fund_id": fund_id},
        )

        # Get fund repository and retrieve fund
        fund_repo = get_fund_repository()
        fund = fund_repo.get_by_fund_id(fund_id)

        if not fund:
            logger.warning(f"Fund not found: {fund_id}")
            return APIResponse.not_found(f"Fund {fund_id} not found")

        # Enrich fund data with analytics
        fund_details = enrich_fund_with_analytics(fund)

        metrics.add_metric(name="FundDetailsRetrieved", unit=MetricUnit.Count, value=1)
        logger.info(f"Successfully retrieved fund details: {fund_id}")

        return APIResponse.success(
            message="Fund details retrieved successfully", data=fund_details
        )

    except Exception as e:
        logger.exception("Error in get fund details handler")
        metrics.add_metric(name="GetFundDetailsErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to retrieve fund details")


@tracer.capture_method
def handle_get_fund_historical(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds/{fund_id}/historical - Get fund historical data."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id from path
        fund_id = extract_fund_id_from_path(event.get("path", ""))
        if not fund_id:
            return APIResponse.validation_error("Fund ID is required")

        # Parse query parameters
        query_params = event.get("queryStringParameters") or {}
        period = query_params.get("period", "1Y")
        include_benchmark = (
            query_params.get("include_benchmark", "false").lower() == "true"
        )

        logger.info(
            "Processing get fund historical data request",
            extra={
                "user_id": user_context.get("user_id"),
                "fund_id": fund_id,
                "period": period,
                "include_benchmark": include_benchmark,
            },
        )

        # Get fund repository and retrieve fund
        fund_repo = get_fund_repository()
        fund = fund_repo.get_by_fund_id(fund_id)

        if not fund:
            logger.warning(f"Fund not found: {fund_id}")
            return APIResponse.not_found(f"Fund {fund_id} not found")

        # Get historical data from database only
        historical_data = get_fund_historical_data_from_db(
            fund, period, include_benchmark
        )

        metrics.add_metric(
            name="FundHistoricalDataRetrieved", unit=MetricUnit.Count, value=1
        )
        logger.info(f"Successfully retrieved fund historical data: {fund_id}")

        return APIResponse.success(
            message="Fund historical data retrieved successfully", data=historical_data
        )

    except Exception as e:
        logger.exception("Error in get fund historical data handler")
        metrics.add_metric(
            name="GetFundHistoricalErrors", unit=MetricUnit.Count, value=1
        )
        return APIResponse.internal_server_error(
            "Failed to retrieve fund historical data"
        )


@tracer.capture_method
def handle_create_fund(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /funds - Create new fund."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        logger.info(
            "Processing create fund request",
            extra={"user_id": user_context.get("user_id")},
        )

        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)
        logger.info(f"Raw request body: {body}")
        try:
            fund_request = FundCreateRequest(**body)
            logger.info(f"FundCreateRequest created successfully")
        except ValidationError as e:
            logger.warning(f"Invalid fund creation request: {e}")
            return APIResponse.validation_error(
                "Invalid fund data", [dict(error) for error in e.errors()]
            )

        # Enhanced server-side validation
        validation_service = FundValidationService()
        validation_result = validation_service.validate_fund_creation(
            fund_request, user_context
        )

        if not validation_result.is_valid:
            logger.warning(
                f"Fund creation validation failed: {len(validation_result.errors)} errors"
            )
            return ValidationResponseHandler.create_validation_error_response(
                validation_result
            )

        # Check if fund ID already exists
        fund_repo = get_fund_repository()
        existing_fund = fund_repo.get_by_fund_id(fund_request.fund_id)
        if existing_fund:
            logger.warning(f"Fund already exists: {fund_request.fund_id}")
            return APIResponse.validation_error(
                f"Fund {fund_request.fund_id} already exists",
                [
                    {
                        "field": "fund_id",
                        "message": f"Fund {fund_request.fund_id} already exists",
                    }
                ],
            )

        # Create fund object
        fund_data = fund_request.model_dump(
            by_alias=True
        )  # Use aliases for field names
        fund_data["created_at"] = datetime.now(timezone.utc)
        fund_data["updated_at"] = datetime.now(timezone.utc)

        logger.info(f"Fund data before Fund creation: {fund_data}")
        fund = Fund(**fund_data)
        logger.info(f"Fund object created: {fund.model_dump()}")

        # Check holdings specifically
        if fund.holdings:
            logger.info(f"Holdings in fund object: {fund.holdings.model_dump()}")
        else:
            logger.info("No holdings in fund object")

        # Save to database
        created_fund = fund_repo.create(fund)
        # Skip FundResponse to avoid model rebuild issues
        fund_response = created_fund

        metrics.add_metric(name="FundCreated", unit=MetricUnit.Count, value=1)
        logger.info(f"Successfully created fund: {fund_request.fund_id}")

        # Include validation warnings in successful response if any
        response_data: Dict[str, Any] = {
            "fund": fund_response.model_dump(by_alias=True)
        }
        if validation_result.warnings:
            response_data["validation_warnings"] = validation_result.warnings

        return APIResponse.created(
            message="Fund created successfully", data=response_data
        )

    except Exception as e:
        logger.exception("Error in create fund handler")
        metrics.add_metric(name="CreateFundErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to create fund")


@tracer.capture_method
def handle_update_fund(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle PUT /funds/{fund_id} - Update fund."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id from path
        fund_id = extract_fund_id_from_path(event.get("path", ""))
        if not fund_id:
            return APIResponse.validation_error("Fund ID is required")

        logger.info(
            "Processing update fund request",
            extra={"user_id": user_context.get("user_id"), "fund_id": fund_id},
        )

        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)

        # Store analytics data if present, then remove it from body for normal validation
        analytics_data = None
        if "analytics" in body:
            logger.info(
                "Processing analytics update for fund", extra={"fund_id": fund_id}
            )
            analytics_data = body.pop("analytics")  # Remove analytics from body

        # Continue with normal validation
        try:
            update_request = FundUpdateRequest(**body)
        except ValidationError as e:
            logger.warning(f"Invalid fund update request: {e}")
            return APIResponse.validation_error(
                "Invalid fund update data", [dict(error) for error in e.errors()]
            )

        # Get existing fund
        fund_repo = get_fund_repository()
        existing_fund = fund_repo.get_by_fund_id(fund_id)
        if not existing_fund:
            logger.warning(f"Fund not found for update: {fund_id}")
            return APIResponse.not_found(f"Fund {fund_id} not found")

        # Enhanced server-side validation for updates
        validation_service = FundValidationService()
        validation_result = validation_service.validate_fund_update(
            update_request, existing_fund, user_context
        )

        if not validation_result.is_valid:
            logger.warning(
                f"Fund update validation failed: {len(validation_result.errors)} errors"
            )
            return ValidationResponseHandler.create_validation_error_response(
                validation_result
            )

        # Update fund with provided data
        update_data = update_request.model_dump(exclude_unset=True)
        if update_data:
            # Update fields
            for field, value in update_data.items():
                if hasattr(existing_fund, field):
                    setattr(existing_fund, field, value)

        # Process analytics data if present
        if analytics_data:
            logger.info(
                "Applying analytics updates to fund", extra={"fund_id": fund_id}
            )
            updated_performance = convert_analytics_to_performance_metrics(
                analytics_data, existing_fund.performance_metrics
            )
            existing_fund.performance_metrics = updated_performance
            logger.info(
                "Successfully applied analytics updates", extra={"fund_id": fund_id}
            )

        # Set updated timestamp if any changes were made
        if update_data or analytics_data:
            existing_fund.updated_at = datetime.now(timezone.utc)

        # Save updated fund if any changes were made
        if update_data or analytics_data:
            updated_fund = fund_repo.update(existing_fund)

            # Create response data without using FundResponse class to avoid forward reference issues
            fund_response_data = updated_fund.model_dump(by_alias=True)

            metrics.add_metric(name="FundUpdated", unit=MetricUnit.Count, value=1)
            logger.info(f"Successfully updated fund: {fund_id}")

            # Include validation warnings in successful response if any
            response_data: Dict[str, Any] = {"fund": fund_response_data}
            if validation_result.warnings:
                response_data["validation_warnings"] = validation_result.warnings

            return APIResponse.success(
                message="Fund updated successfully", data=response_data
            )
        else:
            return APIResponse.validation_error("No valid update data provided")

    except Exception as e:
        logger.exception("Error in update fund handler")
        metrics.add_metric(name="UpdateFundErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to update fund")


@tracer.capture_method
def handle_delete_fund(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle DELETE /funds/{fund_id} - Delete fund (soft delete)."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id from path
        fund_id = extract_fund_id_from_path(event.get("path", ""))
        if not fund_id:
            return APIResponse.validation_error("Fund ID is required")

        logger.info(
            "Processing delete fund request",
            extra={"user_id": user_context.get("user_id"), "fund_id": fund_id},
        )

        # Get fund repository
        fund_repo = get_fund_repository()

        # Check if fund exists
        existing_fund = fund_repo.get_by_fund_id(fund_id)
        if not existing_fund:
            logger.warning(f"Fund not found for deletion: {fund_id}")
            return APIResponse.not_found(f"Fund {fund_id} not found")

        # Soft delete by updating status to CLOSED
        success = fund_repo.soft_delete_fund(fund_id, user_context.get("user_id", ""))

        if success:
            metrics.add_metric(name="FundDeleted", unit=MetricUnit.Count, value=1)
            logger.info(f"Successfully deleted fund: {fund_id}")

            return APIResponse.success(
                message="Fund deleted successfully",
                data={"fund_id": fund_id, "deleted": True},
            )
        else:
            return APIResponse.internal_server_error("Failed to delete fund")

    except Exception as e:
        logger.exception("Error in delete fund handler")
        metrics.add_metric(name="DeleteFundErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to delete fund")


@tracer.capture_method
def handle_bulk_update_funds(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /funds/bulk-update - Bulk update multiple funds with validation."""

    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        logger.info(
            "Processing bulk fund update request",
            extra={"user_id": user_context.get("user_id")},
        )

        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)

        # Validate bulk operation structure
        if not isinstance(body, dict) or "updates" not in body:
            return APIResponse.validation_error("Request must contain 'updates' array")

        updates = body.get("updates", [])
        if not isinstance(updates, list) or len(updates) == 0:
            return APIResponse.validation_error(
                "Updates array cannot be empty",
                [{"field": "updates", "message": "Updates array cannot be empty"}],
            )

        if len(updates) > 100:  # Limit bulk operations
            return APIResponse.validation_error(
                "Bulk update limited to 100 funds per request",
                [
                    {
                        "field": "updates",
                        "message": "Bulk update limited to 100 funds per request",
                    }
                ],
            )

        # Enhanced server-side validation for bulk operations
        validation_service = FundValidationService()
        validation_results = validation_service.validate_bulk_fund_updates(
            updates, user_context
        )

        # Check if any validation failed
        failed_validations = {
            fund_id: result
            for fund_id, result in validation_results.items()
            if not result.is_valid
        }

        if failed_validations:
            logger.warning(
                f"Bulk update validation failed for {len(failed_validations)} funds"
            )
            return ValidationResponseHandler.create_bulk_validation_response(
                validation_results
            )

        # Process individual fund updates
        fund_repo = get_fund_repository()
        update_results = []

        for update_data in updates:
            fund_id = update_data.get("fund_id")
            if not fund_id:
                continue

            try:
                # Get existing fund
                existing_fund = fund_repo.get_by_fund_id(fund_id)
                if not existing_fund:
                    update_results.append(
                        {
                            "fund_id": fund_id,
                            "success": False,
                            "error": "Fund not found",
                        }
                    )
                    continue

                # Create update request
                update_request = FundUpdateRequest(**update_data.get("data", {}))

                # Validate individual update
                individual_validation = validation_service.validate_fund_update(
                    update_request, existing_fund, user_context
                )

                if not individual_validation.is_valid:
                    update_results.append(
                        {
                            "fund_id": fund_id,
                            "success": False,
                            "error": "Validation failed",
                            "validation_errors": individual_validation.errors,
                        }
                    )
                    continue

                # Apply updates
                update_fields = update_request.model_dump(exclude_unset=True)
                if update_fields:
                    for field, value in update_fields.items():
                        if hasattr(existing_fund, field):
                            setattr(existing_fund, field, value)

                    existing_fund.updated_at = datetime.now(timezone.utc)
                    updated_fund = fund_repo.update(existing_fund)

                    update_results.append(
                        {
                            "fund_id": fund_id,
                            "success": True,
                            "warnings": (
                                individual_validation.warnings
                                if individual_validation.warnings
                                else None
                            ),
                        }
                    )
                else:
                    update_results.append(
                        {
                            "fund_id": fund_id,
                            "success": False,
                            "error": "No valid update data provided",
                        }
                    )

            except Exception as e:
                logger.exception(f"Error updating fund {fund_id}")
                update_results.append(
                    {"fund_id": fund_id, "success": False, "error": str(e)}
                )

        successful_updates = sum(
            1 for result in update_results if result.get("success")
        )
        failed_updates = len(update_results) - successful_updates

        metrics.add_metric(
            name="BulkFundUpdates", unit=MetricUnit.Count, value=successful_updates
        )
        if failed_updates > 0:
            metrics.add_metric(
                name="BulkFundUpdateErrors", unit=MetricUnit.Count, value=failed_updates
            )

        logger.info(
            f"Bulk update completed: {successful_updates} successful, {failed_updates} failed"
        )

        return APIResponse.success(
            message=f"Bulk update completed: {successful_updates} successful, {failed_updates} failed",
            data={
                "summary": {
                    "total_requested": len(updates),
                    "successful": successful_updates,
                    "failed": failed_updates,
                },
                "results": update_results,
            },
        )

    except Exception as e:
        logger.exception("Error in bulk fund update handler")
        metrics.add_metric(name="BulkFundUpdateErrors", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to process bulk fund updates")


def extract_fund_id_from_path(path: str) -> Optional[str]:
    """
    Extract fund ID from API Gateway path.

    Args:
        path: API Gateway path like '/api/funds/FUND-123' or '/funds/FUND-123/details'

    Returns:
        Fund ID or None if not found
    """
    try:
        # Split path and find the fund ID after '/funds/'
        path_parts = path.split("/")
        funds_index = path_parts.index("funds")
        if funds_index + 1 < len(path_parts):
            return path_parts[funds_index + 1]
        return None
    except (ValueError, IndexError):
        return None


def get_actual_asset_allocation(fund: Fund) -> Dict[str, float]:
    """
    Extract actual asset allocation from fund holdings data only.

    Args:
        fund: Fund object with holdings data

    Returns:
        Dictionary with asset allocation percentages (zeros if no data)
    """
    # Return actual asset allocation from holdings only
    if fund.holdings and fund.holdings.asset_allocation:
        asset_allocation = fund.holdings.asset_allocation

        # Convert from DynamoDB format (Decimal strings) to frontend format
        return {
            "stocks": round(
                float(
                    asset_allocation.get("Equity", asset_allocation.get("equity", 0))
                ),
                2,
            ),
            "bonds": round(
                float(asset_allocation.get("Debt", asset_allocation.get("debt", 0))), 2
            ),
            "cash": round(
                float(asset_allocation.get("Cash", asset_allocation.get("cash", 0))), 2
            ),
            "other": round(
                float(asset_allocation.get("Others", asset_allocation.get("other", 0))),
                2,
            ),
        }

    # No fallback - return zeros if no data available
    return {
        "stocks": 0.0,
        "bonds": 0.0,
        "cash": 0.0,
        "other": 0.0,
    }


def get_actual_geographic_allocation(fund: Fund) -> Dict[str, float]:
    """
    Extract actual geographic allocation from fund holdings data only.

    Args:
        fund: Fund object with holdings data

    Returns:
        Dictionary with geographic allocation percentages (empty if no data)
    """
    # Return actual geographic allocation from holdings only
    if fund.holdings and fund.holdings.geographic_allocation:
        geo_allocation = fund.holdings.geographic_allocation

        # Convert from DynamoDB format (Decimal strings) to frontend format
        # Use actual DynamoDB keys instead of mapping to generic categories
        result = {}
        for key, value in geo_allocation.items():
            result[key] = round(float(value), 2)

        return result

    # No fallback - return empty dict if no data available
    return {}


def enrich_fund_with_analytics(fund: Fund) -> Dict[str, Any]:
    """
    Enrich basic fund data with detailed analytics and KPIs from database only.
    Includes latest snapshot data if available.

    Args:
        fund: Basic fund object from database

    Returns:
        Dictionary with enriched fund data including analytics and latest snapshot
    """
    from decimal import Decimal

    # Convert basic fund to dict
    fund_dict = fund.model_dump(by_alias=True)

    # Get latest snapshot data
    snapshot_repo = get_fund_snapshot_repository()
    latest_snapshot = snapshot_repo.get_latest_snapshot(fund.fund_id)

    # If snapshot exists, override fund data with snapshot values
    if latest_snapshot:
        logger.info(
            f"Using latest snapshot data for fund {fund.fund_id}, month {latest_snapshot.snapshot_month}"
        )

        # Override NAV and total assets with snapshot values
        if latest_snapshot.nav:
            fund_dict["nav"] = str(latest_snapshot.nav)
        if latest_snapshot.total_assets:
            fund_dict["total_assets"] = str(latest_snapshot.total_assets)

        # Override performance metrics if available in snapshot
        if latest_snapshot.performance_metrics:
            fund_dict["performance_metrics"] = (
                latest_snapshot.performance_metrics.model_dump()
            )
            performance = latest_snapshot.performance_metrics
        else:
            performance = fund.performance_metrics

        # Override holdings if available in snapshot
        if latest_snapshot.holdings:
            fund_dict["holdings"] = latest_snapshot.holdings.model_dump()

        # Add snapshot metadata
        fund_dict["snapshot_info"] = {
            "snapshot_month": latest_snapshot.snapshot_month,
            "last_updated": latest_snapshot.updated_at.isoformat(),
            "created_by": latest_snapshot.created_by,
            "notes": latest_snapshot.notes,
            "data_sources": latest_snapshot.data_sources,
        }
    else:
        performance = fund.performance_metrics

    # Use performance data (either from snapshot or fund)
    base_return = (
        float(performance.one_year_return)
        if performance and performance.one_year_return
        else 0.0
    )
    volatility = (
        float(performance.volatility) if performance and performance.volatility else 0.0
    )

    # Extract actual analytics from database, no fallbacks
    fund_dict["analytics"] = {
        "kpis": {
            "totalReturn": base_return,
            "annualizedReturn": (
                float(performance.one_year_return)
                if performance and performance.one_year_return
                else 0.0
            ),
            "volatility": volatility,
            "sharpeRatio": (
                float(performance.sharpe_ratio)
                if performance and performance.sharpe_ratio
                else 0.0
            ),
            "alpha": (
                float(performance.alpha) if performance and performance.alpha else 0.0
            ),
            "beta": (
                float(performance.beta) if performance and performance.beta else 1.0
            ),
            "maxDrawdown": (
                float(performance.max_drawdown)
                if performance and performance.max_drawdown
                else 0.0
            ),
            "informationRatio": (
                float(performance.information_ratio)
                if performance and performance.information_ratio
                else 0.0
            ),
            "sortinoRatio": (
                float(performance.sortino_ratio)
                if performance and performance.sortino_ratio
                else 0.0
            ),
            "calmarRatio": (
                float(performance.calmar_ratio)
                if performance and performance.calmar_ratio
                else 0.0
            ),
            "treynorRatio": (
                float(performance.treynor_ratio)
                if performance and performance.treynor_ratio
                else 0.0
            ),
            "trackingError": (
                float(performance.tracking_error)
                if performance and performance.tracking_error
                else 0.0
            ),
        },
        "riskMetrics": {
            "standardDeviation": volatility,
            "downSideRisk": (
                float(performance.downside_deviation)
                if performance and performance.downside_deviation
                else 0.0
            ),
            "downsideDeviation": (
                float(performance.downside_deviation)
                if performance and performance.downside_deviation
                else 0.0
            ),
            "varRisk": (
                float(performance.var_1d_95)
                if performance and performance.var_1d_95
                else 0.0
            ),
            "var1d95": (
                float(performance.var_1d_95)
                if performance and performance.var_1d_95
                else 0.0
            ),
            "var1d99": (
                float(performance.var_1d_99)
                if performance and performance.var_1d_99
                else 0.0
            ),
            "cvar1d95": 0.0,  # Not available in PerformanceMetrics model
            "cvar1d99": 0.0,  # Not available in PerformanceMetrics model
            "sortRatio": (
                float(performance.sortino_ratio)
                if performance and performance.sortino_ratio
                else 0.0
            ),
            "calmarRatio": (
                float(performance.calmar_ratio)
                if performance and performance.calmar_ratio
                else 0.0
            ),
            "correlation": (
                float(performance.correlation)
                if performance and performance.correlation
                else 0.0
            ),
        },
        "assetAllocation": get_actual_asset_allocation(fund),
        "geographicAllocation": get_actual_geographic_allocation(fund),
        "topHoldings": get_actual_top_holdings(fund),
        "sectorAllocation": get_actual_sector_allocation(fund),
    }

    # Add historical data from database only
    historical_data_result = get_fund_historical_data_from_db(fund, "1Y", False)
    fund_dict["historicalData"] = historical_data_result.get("data", [])

    # Add benchmark comparison from database only
    fund_dict["benchmark"] = {
        "name": fund.benchmark or get_benchmark_for_fund(fund),
        "symbol": f"{fund.fund_type.upper()}IDX",
        "performance": {
            "oneDay": (
                float(performance.one_day_return)
                if performance and performance.one_day_return
                else 0.0
            ),
            "oneWeek": (
                float(performance.one_week_return)
                if performance and performance.one_week_return
                else 0.0
            ),
            "oneMonth": (
                float(performance.one_month_return)
                if performance and performance.one_month_return
                else 0.0
            ),
            "threeMonths": (
                float(performance.three_month_return)
                if performance and performance.three_month_return
                else 0.0
            ),
            "sixMonths": (
                float(performance.six_month_return)
                if performance and performance.six_month_return
                else 0.0
            ),
            "oneYear": (
                float(performance.one_year_return)
                if performance and performance.one_year_return
                else 0.0
            ),
            "threeYears": (
                float(performance.three_year_return)
                if performance and performance.three_year_return
                else 0.0
            ),
            "fiveYears": (
                float(performance.five_year_return)
                if performance and performance.five_year_return
                else 0.0
            ),
        },
    }

    # Add documents (mock for now)
    fund_dict["documents"] = [
        {
            "id": f"{fund.fund_id}_factsheet",
            "name": "Fund Factsheet",
            "type": "factsheet",
            "url": f"/documents/{fund.fund_id}/factsheet.pdf",
            "uploadDate": fund.created_at.isoformat(),
        },
        {
            "id": f"{fund.fund_id}_prospectus",
            "name": "Fund Prospectus",
            "type": "prospectus",
            "url": f"/documents/{fund.fund_id}/prospectus.pdf",
            "uploadDate": fund.created_at.isoformat(),
        },
    ]

    return fund_dict


def get_actual_top_holdings(fund: Fund) -> List[Dict[str, Any]]:
    """Extract actual top holdings for a fund from database only."""

    # First, try to use actual holdings data from DynamoDB
    if fund.holdings and fund.holdings.top_holdings:
        logger.info(
            f"Using actual holdings data for fund {fund.fund_id}: {len(fund.holdings.top_holdings)} holdings found"
        )

        # Debug: Log the structure of the first holding to understand the data format
        if fund.holdings.top_holdings:
            sample_holding = fund.holdings.top_holdings[0]
            logger.info(f"Sample holding data structure: {sample_holding}")
            logger.info(
                f"Available fields in sample holding: {list(sample_holding.keys()) if isinstance(sample_holding, dict) else 'Not a dict'}"
            )

        holdings_list = []
        for holding in fund.holdings.top_holdings:
            if isinstance(holding, dict):
                # Extract ticker/symbol - DynamoDB uses "ticker" field, but frontend expects "symbol"
                ticker_symbol = holding.get("ticker") or holding.get("symbol", "N/A")

                # Transform DynamoDB holding data to frontend format
                holding_data = {
                    "id": holding.get("id")
                    or ticker_symbol
                    or f"{fund.fund_id}_{ticker_symbol}",
                    "name": holding.get("name", "Unknown"),
                    "symbol": ticker_symbol,
                    "sector": holding.get("sector", "Unknown"),
                    "percentage": 0.0,
                    "marketValue": 0.0,
                }

                # Handle percentage field - it might be string, Decimal, or missing
                if "percentage" in holding and holding["percentage"] is not None:
                    try:
                        holding_data["percentage"] = float(holding["percentage"])
                    except (ValueError, TypeError):
                        holding_data["percentage"] = 0.0
                        logger.warning(
                            f"Could not convert percentage for holding {holding.get('name')}: {holding.get('percentage')}"
                        )
                else:
                    # If no percentage, this holding might be excluded from percentage calculations
                    # For display purposes, assign a small placeholder percentage
                    holding_data["percentage"] = 0.0

                # Handle market value calculation
                if "marketValue" in holding or "market_value" in holding:
                    try:
                        market_value = holding.get("marketValue") or holding.get(
                            "market_value", 0
                        )
                        holding_data["marketValue"] = float(market_value)
                    except (ValueError, TypeError):
                        # Calculate market value from percentage and total assets if possible
                        if fund.total_assets and holding_data["percentage"] > 0:
                            holding_data["marketValue"] = float(fund.total_assets) * (
                                holding_data["percentage"] / 100
                            )
                        else:
                            holding_data["marketValue"] = 0.0
                elif fund.total_assets and holding_data["percentage"] > 0:
                    # Calculate market value from percentage and total assets
                    holding_data["marketValue"] = float(fund.total_assets) * (
                        holding_data["percentage"] / 100
                    )
                else:
                    # No market value or percentage data available
                    holding_data["marketValue"] = 0.0

                # Include any additional fields that might be present
                for field in ["country", "currency", "isin", "exchange"]:
                    if field in holding:
                        holding_data[field] = holding[field]

                holdings_list.append(holding_data)

        # Sort by percentage descending to show largest holdings first
        holdings_list.sort(key=lambda x: x["percentage"], reverse=True)

        logger.info(
            f"Successfully processed {len(holdings_list)} actual holdings for fund {fund.fund_id}"
        )
        return holdings_list

    # No fallback - return empty list if no holdings data available
    logger.warning(f"No actual holdings data found for fund {fund.fund_id}")
    return []


def get_actual_sector_allocation(fund: Fund) -> List[Dict[str, Any]]:
    """Extract actual sector allocation for a fund from database only."""

    # First, try to use actual sector allocation data from DynamoDB
    if fund.holdings and fund.holdings.sector_allocation:
        logger.info(
            f"Using actual sector allocation data for fund {fund.fund_id}: {len(fund.holdings.sector_allocation)} sectors found"
        )

        sector_list = []
        total_assets = float(fund.total_assets or 1000000)

        for sector_name, percentage in fund.holdings.sector_allocation.items():
            try:
                percentage_float = float(percentage)
                if percentage_float > 0:  # Only include sectors with >0% allocation
                    market_value = total_assets * (percentage_float / 100)
                    sector_list.append(
                        {
                            "name": sector_name,
                            "percentage": round(percentage_float, 2),
                            "marketValue": round(market_value, 2),
                            "change": 0.0,  # We don't have historical change data, so default to 0
                        }
                    )
            except (ValueError, TypeError):
                logger.warning(
                    f"Could not convert sector allocation percentage for {sector_name}: {percentage}"
                )
                continue

        # Sort by percentage descending
        sector_list.sort(key=lambda x: x["percentage"], reverse=True)

        logger.info(
            f"Successfully processed {len(sector_list)} actual sectors for fund {fund.fund_id}"
        )
        return sector_list

    # No fallback - return empty list if no sector allocation data available
    logger.warning(f"No actual sector allocation data found for fund {fund.fund_id}")
    return []


def get_benchmark_for_fund(fund: Fund) -> str:
    """Get appropriate benchmark name for fund type."""
    benchmark_map = {
        # Primary frontend types
        "mutual_fund": "NIFTY 50",
        "etf": "NIFTY 50",
        "index_fund": "NIFTY 50",
        "bond_fund": "NIFTY 10 Year Benchmark G-Sec",
        "money_market": "NIFTY 1D Rate Index",
        # Legacy backend types (for compatibility)
        "equity": "NIFTY 50",
        "bond": "NIFTY 10 Year Benchmark G-Sec",
        "mixed": "NIFTY 50 Hybrid Composite Debt 65:35",
        "alternative": "NIFTY 50",
        "index": "NIFTY 50",
    }
    return benchmark_map.get(fund.fund_type, "NIFTY 50")


@tracer.capture_method
def handle_market_data_input(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle POST /funds/{fund_id}/market-data - Submit market data input.

    Args:
        event: Lambda event containing the request

    Returns:
        API response with market data input confirmation
    """
    try:
        # Extract fund ID from path
        fund_id = extract_fund_id_from_path(event.get("pathParameters", {}))
        if not fund_id:
            return APIResponse.validation_error(
                "Fund ID is required",
                [{"field": "fund_id", "message": "Fund ID is required"}],
            )

        # Get user context
        user_context = create_user_context(event)

        logger.info(
            "Processing market data input request",
            extra={
                "user_id": user_context.get("user_id"),
                "fund_id": fund_id,
            },
        )

        # Parse and validate request body
        body = RequestValidator.validate_json_body(event)
        try:
            market_data_input = MarketDataInput(**body)
        except ValidationError as e:
            logger.warning(f"Invalid market data input request: {e}")
            return APIResponse.validation_error(
                "Invalid market data input", [dict(error) for error in e.errors()]
            )

        # Verify fund exists
        fund_repo = get_fund_repository()
        fund = fund_repo.get_by_fund_id(fund_id)
        if not fund:
            logger.warning(f"Fund not found: {fund_id}")
            return APIResponse.not_found(f"Fund {fund_id} not found")

        # Set fund_id and input_by from context
        market_data_input.fund_id = fund_id
        market_data_input.input_by = user_context.get("user_id", "unknown")

        # Store market data input (this would go to the enhanced DynamoDB table)
        market_data_repo = get_market_data_repository()
        stored_input = market_data_repo.store_market_data_input(market_data_input)

        # If validated, also update the fund's current market data
        if market_data_input.validated:
            update_fund_market_data(fund, market_data_input)

        metrics.add_metric(
            name="MarketDataInputSubmitted", unit=MetricUnit.Count, value=1
        )

        logger.info(f"Successfully stored market data input for fund: {fund_id}")

        return APIResponse.success(
            {
                "message": "Market data input submitted successfully",
                "input_id": stored_input.get("input_id"),
                "fund_id": fund_id,
                "validated": market_data_input.validated,
            }
        )

    except ValidationError as e:
        logger.warning(f"Validation error in market data input: {e}")
        return APIResponse.validation_error(
            "Invalid market data input", [dict(error) for error in e.errors()]
        )
    except Exception as e:
        logger.error(f"Error processing market data input: {e}")
        return APIResponse.internal_server_error("Failed to process market data input")


def get_market_data_repository():
    """Get market data repository instance."""
    from shared.repositories.market_data_repository import MarketDataRepository

    return MarketDataRepository()


def update_fund_market_data(fund: Fund, market_data_input: MarketDataInput):
    """Update fund's current market data based on validated input."""
    from datetime import datetime, timezone

    logger.info(
        f"Updating fund market data from validated input",
        extra={
            "fund_id": fund.fund_id,
            "input_by": market_data_input.input_by,
            "validated": market_data_input.validated,
        },
    )

    # Update fund's NAV if provided
    if market_data_input.nav is not None:
        old_nav = fund.nav
        fund.nav = market_data_input.nav
        logger.info(f"Updated fund NAV: {old_nav} -> {fund.nav}")

    # Update other market data fields if provided
    if market_data_input.market_price is not None:
        # For funds, market_price can be used to update current_price or similar field
        # Since Fund model doesn't have market_price, we'll log it
        logger.info(f"Market price provided: {market_data_input.market_price}")

    # Update timestamp to reflect market data update
    fund.updated_at = datetime.now(timezone.utc)

    # Save the updated fund to database
    fund_repo = get_fund_repository()
    updated_fund = fund_repo.update(fund)

    logger.info(
        f"Successfully updated fund market data in database",
        extra={
            "fund_id": fund.fund_id,
            "updated_nav": str(fund.nav) if fund.nav else None,
        },
    )


@tracer.capture_method
def handle_get_market_data(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle GET /funds/{fund_id}/market-data - Get comprehensive market data.

    Args:
        event: Lambda event containing the request

    Returns:
        API response with market data summary
    """
    try:
        # Validate session
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        # Extract fund ID from path
        fund_id = extract_fund_id_from_path(event.get("path", ""))
        if not fund_id:
            return APIResponse.validation_error("Fund ID is required")

        # Get user context
        user_context = create_user_context(session_result.get("user_info", {}))

        logger.info(
            "Processing get market data request",
            extra={
                "user_id": user_context.get("user_id"),
                "fund_id": fund_id,
            },
        )

        # Verify fund exists
        fund_repo = get_fund_repository()
        fund = fund_repo.get_by_fund_id(fund_id)
        if not fund:
            logger.warning(f"Fund not found: {fund_id}")
            return APIResponse.not_found(f"Fund {fund_id} not found")

        # Get market data summary from database only
        market_data_summary = get_market_data_summary_from_db(fund)

        metrics.add_metric(name="MarketDataRetrieved", unit=MetricUnit.Count, value=1)

        logger.info(f"Successfully retrieved market data for fund: {fund_id}")

        return APIResponse.success(
            message="Market data retrieved successfully", data=market_data_summary
        )

    except Exception as e:
        logger.error(f"Error retrieving market data: {e}")
        return APIResponse.internal_server_error("Failed to retrieve market data")


def get_market_data_summary_from_db(fund: Fund) -> Dict[str, Any]:
    """
    Get market data summary for a fund from database only.

    Args:
        fund: Fund object

    Returns:
        Dictionary containing market data summary from database
    """
    from datetime import datetime

    # Use only actual NAV and price data from database
    current_nav = float(fund.nav) if fund.nav else 0.0

    # Use actual change percent from performance metrics
    change_percent = 0.0
    if fund.performance_metrics and fund.performance_metrics.one_day_return:
        change_percent = float(fund.performance_metrics.one_day_return)

    price_data = {
        "current_price": current_nav,
        "previous_close": (
            round(current_nav * (1 - change_percent / 100), 2)
            if change_percent != 0
            else current_nav
        ),
        "open": 0.0,  # Not available in database
        "high": 0.0,  # Not available in database
        "low": 0.0,  # Not available in database
        "volume": 0,  # Not available in database
        "average_volume": 0,  # Not available in database
        "market_cap": float(fund.total_assets) if fund.total_assets else 0.0,
        "pe_ratio": 0.0,  # Not available in database
        "dividend_yield": 0.0,  # Not available in database
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }

    # Use actual valuation metrics from database
    valuation_metrics = {
        "nav": current_nav,
        "discount_premium": 0.0,  # Not available in database
        "expense_ratio": float(fund.expense_ratio) if fund.expense_ratio else 0.0,
        "turnover_ratio": 0.0,  # Not available in database
        "tracking_error": (
            float(fund.performance_metrics.tracking_error)
            if fund.performance_metrics and fund.performance_metrics.tracking_error
            else 0.0
        ),
        "information_ratio": (
            float(fund.performance_metrics.information_ratio)
            if fund.performance_metrics and fund.performance_metrics.information_ratio
            else 0.0
        ),
    }

    # Use actual technical indicators from database (if available)
    technical_indicators = {
        "rsi": 0.0,  # Not available in database
        "moving_average_50": 0.0,  # Not available in database
        "moving_average_200": 0.0,  # Not available in database
        "bollinger_upper": 0.0,  # Not available in database
        "bollinger_lower": 0.0,  # Not available in database
        "macd": 0.0,  # Not available in database
        "signal": 0.0,  # Not available in database
    }

    # Use actual risk analytics from database
    volatility = (
        float(fund.performance_metrics.volatility)
        if fund.performance_metrics and fund.performance_metrics.volatility
        else 0.0
    )
    sharpe_ratio = (
        float(fund.performance_metrics.sharpe_ratio)
        if fund.performance_metrics and fund.performance_metrics.sharpe_ratio
        else 0.0
    )
    beta = (
        float(fund.performance_metrics.beta)
        if fund.performance_metrics and fund.performance_metrics.beta
        else 1.0
    )
    sortino_ratio = (
        float(fund.performance_metrics.sortino_ratio)
        if fund.performance_metrics and fund.performance_metrics.sortino_ratio
        else 0.0
    )
    max_drawdown = (
        float(fund.performance_metrics.max_drawdown)
        if fund.performance_metrics and fund.performance_metrics.max_drawdown
        else 0.0
    )
    var_95 = (
        float(fund.performance_metrics.var_1d_95)
        if fund.performance_metrics and fund.performance_metrics.var_1d_95
        else 0.0
    )
    cvar_95 = 0.0  # Not available in PerformanceMetrics model

    risk_analytics = {
        "volatility": volatility,
        "beta": beta,
        "sharpe_ratio": sharpe_ratio,
        "sortino_ratio": sortino_ratio,
        "max_drawdown": max_drawdown,
        "value_at_risk": var_95,
        "conditional_value_at_risk": cvar_95,
    }

    # Data quality metrics
    data_sources = {
        "price_data": "database",
        "fundamentals": "database",
        "technicals": "not_available",
        "risk_metrics": "database",
    }

    return {
        "fund_id": fund.fund_id,
        "fund_name": fund.name,
        "last_updated": datetime.now(timezone.utc).isoformat(),
        "price_data": price_data,
        "valuation_metrics": valuation_metrics,
        "technical_indicators": technical_indicators,
        "risk_analytics": risk_analytics,
        "data_sources": data_sources,
        "overall_quality": "database_only",
    }


def get_fund_historical_data_from_db(
    fund: Fund, period: str, include_benchmark: bool = False
) -> Dict[str, Any]:
    """Get comprehensive historical data for fund from database only."""
    from datetime import timedelta

    # Calculate date range based on period
    end_date = datetime.now(timezone.utc).date()

    if period == "1D":
        start_date = end_date - timedelta(days=1)
    elif period == "1W":
        start_date = end_date - timedelta(weeks=1)
    elif period == "1M":
        start_date = end_date - timedelta(days=30)
    elif period == "3M":
        start_date = end_date - timedelta(days=90)
    elif period == "6M":
        start_date = end_date - timedelta(days=180)
    elif period == "1Y":
        start_date = end_date - timedelta(days=365)
    elif period == "3Y":
        start_date = end_date - timedelta(days=1095)
    elif period == "5Y":
        start_date = end_date - timedelta(days=1825)
    else:
        start_date = end_date - timedelta(days=365)  # Default to 1 year

    # Get historical price repository
    price_repo = get_historical_price_repository()

    # Fetch historical prices from database
    historical_prices = price_repo.get_fund_price_history(
        fund.fund_id, start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")
    )

    # Convert to API format
    fund_data = []
    benchmark_data = []

    for price in reversed(historical_prices):  # Reverse to chronological order
        fund_point = {
            "date": price.date,
            "nav": float(price.nav),
            "value": float(price.nav),  # Add value field for chart compatibility
            "price": float(price.price) if price.price else float(price.nav),
            "volume": float(price.volume) if price.volume else 0,
            "total_return": float(price.total_return) if price.total_return else 0.0,
            "returns": (
                float(price.total_return) if price.total_return else 0.0
            ),  # Add returns field for chart compatibility
        }
        fund_data.append(fund_point)

        # Add benchmark data if requested and available
        if include_benchmark and price.benchmark_value:
            benchmark_point = {
                "date": price.date,
                "value": float(price.benchmark_value),
                "nav": float(price.benchmark_value),
                "total_return": (
                    float(price.benchmark_return) if price.benchmark_return else 0.0
                ),
                "returns": (
                    float(price.benchmark_return) if price.benchmark_return else 0.0
                ),
            }
            benchmark_data.append(benchmark_point)

    # Return data only if we have it from database
    if fund_data:
        result = {"timePeriod": period, "data": fund_data}
        if include_benchmark and benchmark_data:
            result["benchmark"] = {
                "name": fund.benchmark or "Market Index",
                "data": benchmark_data,
            }

        logger.info(
            f"Retrieved {len(fund_data)} historical price points from database for fund: {fund.fund_id}"
        )
        return result

    # No fallback - return empty data if no database records found
    logger.warning(f"No historical data found in database for fund: {fund.fund_id}")
    result = {"timePeriod": period, "data": []}
    if include_benchmark:
        result["benchmark"] = {"name": fund.benchmark or "Market Index", "data": []}
    return result


@tracer.capture_method
def handle_upload_fund_manager_photo(
    event: Dict[str, Any], context: LambdaContext
) -> Dict[str, Any]:
    """
    Handle fund manager photo upload to S3.
    POST /funds/{fund_id}/manager-photo
    """
    try:
        # Extract fund_id from path parameters
        fund_id = event.get("pathParameters", {}).get("fundId")
        if not fund_id:
            return APIResponse.bad_request("Fund ID is required")

        # Validate session
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)
        if not session_result["valid"]:
            return APIResponse.unauthorized(session_result["message"])

        user_context = create_user_context(session_result)

        # Get fund repository
        fund_repo = get_fund_repository()

        # Check if fund exists
        existing_fund = fund_repo.get_by_fund_id(fund_id)
        if not existing_fund:
            return APIResponse.not_found("Fund not found")

        # Parse request body
        try:
            body = json.loads(event.get("body", "{}"))
        except json.JSONDecodeError:
            return APIResponse.bad_request("Invalid JSON in request body")

        # Validate required fields
        if "image_data" not in body:
            return APIResponse.bad_request("image_data is required")

        if "filename" not in body:
            return APIResponse.bad_request("filename is required")

        image_data = body["image_data"]
        filename = body["filename"]

        # Upload photo to S3
        upload_result = photo_manager.upload_base64_photo(
            base64_data=image_data, filename=filename, fund_id=fund_id
        )

        if not upload_result["success"]:
            logger.error(
                f"Failed to upload photo for fund {fund_id}: {upload_result['error']}"
            )
            return APIResponse.bad_request(
                f"Photo upload failed: {upload_result['error']}"
            )

        # Delete old photo if it exists and is an S3 URL
        if existing_fund.fund_manager_photo:
            old_photo_url = existing_fund.fund_manager_photo
            if "s3.amazonaws.com" in old_photo_url:
                deleted = photo_manager.delete_photo_by_url(old_photo_url)
                if deleted:
                    logger.info(f"Deleted old photo: {old_photo_url}")
                else:
                    logger.warning(f"Failed to delete old photo: {old_photo_url}")

        # Update fund with new photo URL
        existing_fund.fund_manager_photo = upload_result["url"]
        existing_fund.updated_at = datetime.now(timezone.utc)

        updated_fund = fund_repo.update(existing_fund)

        # Log success metrics
        metrics.add_metric(name="PhotoUploadSuccess", unit=MetricUnit.Count, value=1)
        metrics.add_metric(
            name="PhotoUploadSize",
            unit=MetricUnit.Bytes,
            value=upload_result["optimized_size"],
        )

        logger.info(f"Successfully uploaded photo for fund {fund_id}")

        response_data = {
            "message": "Photo uploaded successfully",
            "photo_url": upload_result["url"],
            "original_size": upload_result["original_size"],
            "optimized_size": upload_result["optimized_size"],
            "fund_id": fund_id,
        }

        return APIResponse.success(response_data)

    except ValidationError as e:
        logger.error(f"Validation error uploading photo: {str(e)}")
        return APIResponse.bad_request(f"Validation error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error uploading photo: {str(e)}")
        metrics.add_metric(name="PhotoUploadError", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to upload photo")


@tracer.capture_method
def handle_delete_fund_manager_photo(
    event: Dict[str, Any], context: LambdaContext
) -> Dict[str, Any]:
    """
    Handle fund manager photo deletion from S3.
    DELETE /funds/{fund_id}/manager-photo
    """
    try:
        # Extract fund_id from path parameters
        fund_id = event.get("pathParameters", {}).get("fundId")
        if not fund_id:
            return APIResponse.bad_request("Fund ID is required")

        # Validate session
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)
        if not session_result["valid"]:
            return APIResponse.unauthorized(session_result["message"])

        user_context = create_user_context(session_result)

        # Get fund repository
        fund_repo = get_fund_repository()

        # Check if fund exists
        existing_fund = fund_repo.get_by_fund_id(fund_id)
        if not existing_fund:
            return APIResponse.not_found("Fund not found")

        # Delete photo from S3 if it exists
        if existing_fund.fund_manager_photo:
            photo_url = existing_fund.fund_manager_photo
            if "s3.amazonaws.com" in photo_url:
                deleted = photo_manager.delete_photo_by_url(photo_url)
                if not deleted:
                    logger.warning(f"Failed to delete photo from S3: {photo_url}")

        # Clear photo URL from fund
        existing_fund.fund_manager_photo = None
        existing_fund.updated_at = datetime.now(timezone.utc)

        updated_fund = fund_repo.update(existing_fund)

        # Log success metrics
        metrics.add_metric(name="PhotoDeleteSuccess", unit=MetricUnit.Count, value=1)

        logger.info(f"Successfully deleted photo for fund {fund_id}")

        response_data = {"message": "Photo deleted successfully", "fund_id": fund_id}

        return APIResponse.success(response_data)

    except Exception as e:
        logger.error(f"Unexpected error deleting photo: {str(e)}")
        metrics.add_metric(name="PhotoDeleteError", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to delete photo")


# Fund Snapshot Handlers


@tracer.capture_method
def handle_list_fund_snapshots(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds/{fund_id}/snapshots - List fund snapshots."""
    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id from path
        path_params = event.get("pathParameters") or {}
        fund_id = path_params.get("fundId") or path_params.get(
            "id"
        )  # Try both parameter names

        if not fund_id:
            return APIResponse.bad_request("Fund ID is required")

        # Parse query parameters
        query_params = event.get("queryStringParameters") or {}

        try:
            list_params = FundSnapshotListParams(**query_params)
        except ValidationError as e:
            logger.warning(f"Invalid query parameters: {e}")
            return APIResponse.validation_error(
                "Invalid query parameters", [dict(error) for error in e.errors()]
            )

        # Get fund snapshot repository
        snapshot_repo = get_fund_snapshot_repository()

        # List snapshots
        result = snapshot_repo.list_snapshots(
            fund_id=fund_id,
            start_month=list_params.start_month,
            end_month=list_params.end_month,
            limit=list_params.limit,
            last_key=list_params.last_key,
        )

        # Log success metrics
        metrics.add_metric(name="SnapshotListSuccess", unit=MetricUnit.Count, value=1)

        logger.info(
            f"Listed {result['count']} snapshots for fund {fund_id}",
            extra={"fund_id": fund_id, "count": result["count"]},
        )

        return APIResponse.paginated_response(
            data=result["snapshots"],
            count=result["count"],
            has_more="last_evaluated_key" in result,
            last_key=result.get("last_evaluated_key"),
        )

    except Exception as e:
        logger.error(f"Error listing fund snapshots: {str(e)}")
        metrics.add_metric(name="SnapshotListError", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to list fund snapshots")


@tracer.capture_method
def handle_get_fund_snapshot(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle GET /funds/{fund_id}/snapshots/{month} - Get specific snapshot."""
    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id and month from path
        path_params = event.get("pathParameters") or {}
        fund_id = path_params.get("fundId") or path_params.get(
            "id"
        )  # Try both parameter names
        month = path_params.get("month")

        if not fund_id:
            return APIResponse.bad_request("Fund ID is required")
        if not month:
            return APIResponse.bad_request("Month is required")

        # Get fund snapshot repository
        snapshot_repo = get_fund_snapshot_repository()

        # Get snapshot
        snapshot = snapshot_repo.get_snapshot(fund_id, month)

        if not snapshot:
            return APIResponse.not_found(
                f"Snapshot not found for fund {fund_id} and month {month}"
            )

        # Log success metrics
        metrics.add_metric(name="SnapshotGetSuccess", unit=MetricUnit.Count, value=1)

        logger.info(
            f"Retrieved snapshot for fund {fund_id}, month {month}",
            extra={"fund_id": fund_id, "month": month},
        )

        return APIResponse.success(snapshot)

    except Exception as e:
        logger.error(f"Error getting fund snapshot: {str(e)}")
        metrics.add_metric(name="SnapshotGetError", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to get fund snapshot")


@tracer.capture_method
def handle_create_fund_snapshot(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle POST /funds/{fund_id}/snapshots/{month} - Create/update snapshot."""
    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id and month from path
        path_params = event.get("pathParameters") or {}
        fund_id = path_params.get("fundId") or path_params.get(
            "id"
        )  # Try both parameter names
        month = path_params.get("month")

        if not fund_id:
            return APIResponse.bad_request("Fund ID is required")
        if not month:
            return APIResponse.bad_request("Month is required")

        # Parse request body
        try:
            body = json.loads(event.get("body", "{}"))
        except json.JSONDecodeError:
            return APIResponse.bad_request("Invalid JSON in request body")

        # Add month to body data
        body["snapshot_month"] = month

        # Validate snapshot data
        try:
            snapshot_data = FundSnapshotCreate(**body)
        except ValidationError as e:
            logger.warning(f"Invalid snapshot data: {e}")
            return APIResponse.validation_error(
                "Invalid snapshot data", [dict(error) for error in e.errors()]
            )

        # Verify fund exists
        fund_repo = get_fund_repository()
        fund = fund_repo.get_fund(fund_id)
        if not fund:
            return APIResponse.not_found(f"Fund {fund_id} not found")

        # Get fund snapshot repository
        snapshot_repo = get_fund_snapshot_repository()

        # Create or update snapshot
        snapshot = snapshot_repo.create_or_update_snapshot(
            fund_id=fund_id,
            snapshot_data=snapshot_data,
            user_id=user_context.user_id,
        )

        # Log success metrics
        metrics.add_metric(name="SnapshotCreateSuccess", unit=MetricUnit.Count, value=1)

        logger.info(
            f"Created/updated snapshot for fund {fund_id}, month {month}",
            extra={"fund_id": fund_id, "month": month, "user_id": user_context.user_id},
        )

        return APIResponse.success(snapshot, status_code=201)

    except Exception as e:
        logger.error(f"Error creating fund snapshot: {str(e)}")
        metrics.add_metric(name="SnapshotCreateError", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to create fund snapshot")


@tracer.capture_method
def handle_delete_fund_snapshot(event: Dict[str, Any]) -> Dict[str, Any]:
    """Handle DELETE /funds/{fund_id}/snapshots/{month} - Delete snapshot."""
    try:
        # Validate session and get user context
        session_manager = SessionManager()
        session_result = session_manager.validate_session(event)

        if not session_result.get("valid", False):
            return APIResponse.unauthorized("Invalid or expired session")

        user_context = create_user_context(session_result.get("user_info", {}))

        # Extract fund_id and month from path
        path_params = event.get("pathParameters") or {}
        fund_id = path_params.get("fundId") or path_params.get(
            "id"
        )  # Try both parameter names
        month = path_params.get("month")

        if not fund_id:
            return APIResponse.bad_request("Fund ID is required")
        if not month:
            return APIResponse.bad_request("Month is required")

        # Get fund snapshot repository
        snapshot_repo = get_fund_snapshot_repository()

        # Delete snapshot
        deleted = snapshot_repo.delete_snapshot(fund_id, month)

        if not deleted:
            return APIResponse.not_found(
                f"Snapshot not found for fund {fund_id} and month {month}"
            )

        # Log success metrics
        metrics.add_metric(name="SnapshotDeleteSuccess", unit=MetricUnit.Count, value=1)

        logger.info(
            f"Deleted snapshot for fund {fund_id}, month {month}",
            extra={"fund_id": fund_id, "month": month, "user_id": user_context.user_id},
        )

        return APIResponse.success(
            {
                "message": "Snapshot deleted successfully",
                "fund_id": fund_id,
                "month": month,
            }
        )

    except Exception as e:
        logger.error(f"Error deleting fund snapshot: {str(e)}")
        metrics.add_metric(name="SnapshotDeleteError", unit=MetricUnit.Count, value=1)
        return APIResponse.internal_server_error("Failed to delete fund snapshot")
