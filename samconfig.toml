version = 0.1

[default]
[default.global.parameters]
stack_name = "fundflow-dev"

[default.build.parameters]
cached = true
parallel = true

[default.validate.parameters]
lint = true

[default.deploy.parameters]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
resolve_s3 = true
s3_prefix = "fundflow-dev"
region = "ap-northeast-1"
image_repositories = []
parameter_overrides = "Environment=\"dev\" DomainName=\"\" CertificateArn=\"\" LogLevel=\"INFO\" LambdaMemorySize=\"512\" LambdaTimeout=\"30\" ApiThrottleBurstLimit=\"200\" ApiThrottleRateLimit=\"100\" DynamoDBBillingMode=\"PAY_PER_REQUEST\" DynamoDBReadCapacity=\"5\" DynamoDBWriteCapacity=\"5\" BackupRetentionDays=\"30\" LogRetentionDays=\"30\" EnableDetailedMonitoring=\"false\" AlarmNotificationEmail=\"\" ApiCachingEnabled=\"false\" ApiCacheTTL=\"300\" RequestValidationMode=\"basic\" CloudFrontPriceClass=\"PriceClass_100\" CloudFrontCompressionEnabled=\"true\" CloudFrontDefaultTTL=\"86400\" CloudFrontMaxTTL=\"31536000\" EnableWAF=\"false\" SecurityHeadersEnabled=\"true\" IPWhitelistEnabled=\"false\" ExistingUserPoolId=\"us-east-1_Ej8Ej8Ej8\" ExistingUserPoolClientId=\"7ej8ej8ej8ej8ej8ej8ej8ej8ej8\" CreateCognitoResources=\"false\" CognitoUserPoolName=\"fundflow-user-pool\" CognitoClientName=\"fundflow-client\" CognitoResourceServerName=\"fundflow-api\" CallbackURLs=\"http://localhost:3000/api/auth/callback/cognito,https://rmzqmzxtlz.a.pinggy.link/api/auth/callback/cognito\" LogoutURLs=\"http://localhost:3000,https://rmzqmzxtlz.a.pinggy.link\""

[default.package.parameters]
resolve_s3 = true

[default.sync.parameters]
watch = true

[default.local_start_api.parameters]
warm_containers = "EAGER"

[default.local_start_lambda.parameters]
warm_containers = "EAGER"

# Development Environment
[dev]
[dev.deploy.parameters]
stack_name = "fundflow-dev"
profile = "fundflow-dev"
parameter_overrides = "Environment=\"dev\" DomainName=\"\" CertificateArn=\"\" LogLevel=\"DEBUG\" LambdaMemorySize=\"512\" LambdaTimeout=\"30\" ApiThrottleBurstLimit=\"100\" ApiThrottleRateLimit=\"50\" DynamoDBBillingMode=\"PAY_PER_REQUEST\" DynamoDBReadCapacity=\"5\" DynamoDBWriteCapacity=\"5\" BackupRetentionDays=\"7\" LogRetentionDays=\"7\" EnableDetailedMonitoring=\"false\" AlarmNotificationEmail=\"\" ApiCachingEnabled=\"false\" ApiCacheTTL=\"300\" RequestValidationMode=\"basic\" CloudFrontPriceClass=\"PriceClass_100\" CloudFrontCompressionEnabled=\"true\" CloudFrontDefaultTTL=\"86400\" CloudFrontMaxTTL=\"31536000\" EnableWAF=\"false\" SecurityHeadersEnabled=\"true\" IPWhitelistEnabled=\"false\" CreateCognitoResources=\"false\" ExistingUserPoolId=\"ap-northeast-1_H2kKHGUAT\" ExistingUserPoolClientId=\"2jh76f894g6lv9vrus4qbb9hu7\" CognitoUserPoolName=\"fundflow-user-pool\" CognitoClientName=\"fundflow-client\" CognitoResourceServerName=\"fundflow-api\" CallbackURLs=\"http://localhost:3000/api/auth/callback/cognito,https://rmzqmzxtlz.a.pinggy.link/api/auth/callback/cognito\" LogoutURLs=\"http://localhost:3000,https://rmzqmzxtlz.a.pinggy.link\" OpenRouterAPIKey=\"${OPENROUTER_API_KEY}\""
confirm_changeset = false
resolve_s3 = true
s3_prefix = "fundflow-dev"
region = "ap-northeast-1"
capabilities = "CAPABILITY_IAM"
image_repositories = []

# Staging Environment
[staging]
[staging.deploy.parameters]
stack_name = "fundflow-staging"
profile = "fundflow-staging"
parameter_overrides = [
    "ParameterKey=Environment,ParameterValue=staging",
    "ParameterKey=LogLevel,ParameterValue=INFO",
    "ParameterKey=LambdaMemorySize,ParameterValue=1024",
    "ParameterKey=LambdaTimeout,ParameterValue=30",
    "ParameterKey=ApiThrottleBurstLimit,ParameterValue=200",
    "ParameterKey=ApiThrottleRateLimit,ParameterValue=100",
    "ParameterKey=DomainName,ParameterValue=staging-api.fundflow.com",
    "ParameterKey=DynamoDBBillingMode,ParameterValue=PAY_PER_REQUEST",
    "ParameterKey=DynamoDBReadCapacity,ParameterValue=10",
    "ParameterKey=DynamoDBWriteCapacity,ParameterValue=10",
    "ParameterKey=BackupRetentionDays,ParameterValue=30",
    "ParameterKey=LogRetentionDays,ParameterValue=30",
    "ParameterKey=EnableDetailedMonitoring,ParameterValue=true",
    "ParameterKey=AlarmNotificationEmail,ParameterValue=<EMAIL>",
    "ParameterKey=ApiCachingEnabled,ParameterValue=true",
    "ParameterKey=ApiCacheTTL,ParameterValue=300",
    "ParameterKey=RequestValidationMode,ParameterValue=full",
    "ParameterKey=CloudFrontPriceClass,ParameterValue=PriceClass_100",
    "ParameterKey=CloudFrontCompressionEnabled,ParameterValue=true",
    "ParameterKey=CloudFrontDefaultTTL,ParameterValue=86400",
    "ParameterKey=CloudFrontMaxTTL,ParameterValue=31536000",
    "ParameterKey=EnableWAF,ParameterValue=true",
    "ParameterKey=SecurityHeadersEnabled,ParameterValue=true",
    "ParameterKey=IPWhitelistEnabled,ParameterValue=false"
]
confirm_changeset = true

# Production Environment
[prod]
[prod.deploy.parameters]
stack_name = "fundflow-prod"
profile = "fundflow-prod"
parameter_overrides = [
    "ParameterKey=Environment,ParameterValue=prod",
    "ParameterKey=LogLevel,ParameterValue=WARNING",
    "ParameterKey=LambdaMemorySize,ParameterValue=1024",
    "ParameterKey=LambdaTimeout,ParameterValue=60",
    "ParameterKey=ApiThrottleBurstLimit,ParameterValue=500",
    "ParameterKey=ApiThrottleRateLimit,ParameterValue=250",
    "ParameterKey=DomainName,ParameterValue=api.fundflow.com",
    "ParameterKey=CertificateArn,ParameterValue=arn:aws:acm:us-east-1:123456789012:certificate/example-certificate-id",
    "ParameterKey=DynamoDBBillingMode,ParameterValue=PROVISIONED",
    "ParameterKey=DynamoDBReadCapacity,ParameterValue=50",
    "ParameterKey=DynamoDBWriteCapacity,ParameterValue=25",
    "ParameterKey=BackupRetentionDays,ParameterValue=2555",
    "ParameterKey=LogRetentionDays,ParameterValue=365",
    "ParameterKey=EnableDetailedMonitoring,ParameterValue=true",
    "ParameterKey=AlarmNotificationEmail,ParameterValue=<EMAIL>",
    "ParameterKey=ApiCachingEnabled,ParameterValue=true",
    "ParameterKey=ApiCacheTTL,ParameterValue=600",
    "ParameterKey=RequestValidationMode,ParameterValue=full",
    "ParameterKey=CloudFrontPriceClass,ParameterValue=PriceClass_All",
    "ParameterKey=CloudFrontCompressionEnabled,ParameterValue=true",
    "ParameterKey=CloudFrontDefaultTTL,ParameterValue=86400",
    "ParameterKey=CloudFrontMaxTTL,ParameterValue=31536000",
    "ParameterKey=EnableWAF,ParameterValue=true",
    "ParameterKey=SecurityHeadersEnabled,ParameterValue=true",
    "ParameterKey=IPWhitelistEnabled,ParameterValue=true"
]
confirm_changeset = true
fail_on_empty_changeset = false 