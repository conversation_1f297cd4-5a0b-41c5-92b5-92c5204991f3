/**
 * Test suite for Fund Snapshot API client functions
 */

import { fundApi } from '../api';

// Mock the apiRequest function
jest.mock('../api', () => ({
  ...jest.requireActual('../api'),
  __esModule: true,
}));

// Mock fetch globally
global.fetch = jest.fn();

describe('Fund Snapshot API', () => {
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    mockFetch.mockClear();
    console.log = jest.fn(); // Mock console.log to avoid test output noise
  });

  describe('listFundSnapshots', () => {
    it('should fetch fund snapshots successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Snapshots fetched successfully',
        data: [
          {
            fund_id: 'test-fund-123',
            snapshot_month: '2024-12',
            nav: '150.75',
            created_at: '2024-12-01T10:00:00Z',
          },
        ],
        pagination: {
          count: 1,
          has_more: false,
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await fundApi.listFundSnapshots('test-fund-123');

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data[0].fund_id).toBe('test-fund-123');
      expect(result.pagination?.count).toBe(1);
    });

    it('should handle query parameters correctly', async () => {
      const mockResponse = {
        success: true,
        data: [],
        pagination: { count: 0, has_more: false },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await fundApi.listFundSnapshots('test-fund-123', {
        startMonth: '2024-01',
        endMonth: '2024-12',
        limit: 10,
        lastKey: 'some-key',
      });

      const callUrl = mockFetch.mock.calls[0][0] as string;
      expect(callUrl).toContain('start_month=2024-01');
      expect(callUrl).toContain('end_month=2024-12');
      expect(callUrl).toContain('limit=10');
      expect(callUrl).toContain('last_key=some-key');
    });

    it('should handle API errors gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(fundApi.listFundSnapshots('test-fund-123')).rejects.toThrow('Network error');
    });
  });

  describe('getFundSnapshot', () => {
    it('should fetch a specific fund snapshot successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Snapshot fetched successfully',
        data: {
          fund_id: 'test-fund-123',
          snapshot_month: '2024-12',
          nav: '150.75',
          total_assets: '1000000.00',
          created_at: '2024-12-01T10:00:00Z',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await fundApi.getFundSnapshot('test-fund-123', '2024-12');

      expect(result.success).toBe(true);
      expect(result.data.fund_id).toBe('test-fund-123');
      expect(result.data.snapshot_month).toBe('2024-12');
      expect(result.data.nav).toBe('150.75');
    });

    it('should handle snapshot not found', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({
          success: false,
          message: 'Snapshot not found',
        }),
      } as Response);

      await expect(fundApi.getFundSnapshot('test-fund-123', '2024-12')).rejects.toThrow();
    });
  });

  describe('createFundSnapshot', () => {
    it('should create a fund snapshot successfully', async () => {
      const snapshotData = {
        nav: '150.75',
        total_assets: '1000000.00',
        market_data: { price: 150.75, volume: 10000 },
        notes: 'Test snapshot',
      };

      const mockResponse = {
        success: true,
        message: 'Snapshot created successfully',
        data: {
          fund_id: 'test-fund-123',
          snapshot_month: '2024-12',
          ...snapshotData,
          created_at: '2024-12-01T10:00:00Z',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await fundApi.createFundSnapshot('test-fund-123', '2024-12', snapshotData);

      expect(result.success).toBe(true);
      expect(result.data.fund_id).toBe('test-fund-123');
      expect(result.data.snapshot_month).toBe('2024-12');
      expect(result.data.nav).toBe('150.75');

      // Verify the request was made with correct parameters
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/funds/test-fund-123/snapshots/2024-12'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(snapshotData),
        })
      );
    });

    it('should handle validation errors', async () => {
      const invalidData = {
        nav: 'invalid-number',
        snapshot_month: '2024-13', // Invalid month
      };

      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({
          success: false,
          message: 'Validation error',
          errors: [
            { field: 'nav', message: 'Invalid number format' },
            { field: 'snapshot_month', message: 'Invalid month format' },
          ],
        }),
      } as Response);

      await expect(
        fundApi.createFundSnapshot('test-fund-123', '2024-13', invalidData)
      ).rejects.toThrow();
    });

    it('should handle fund not found error', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({
          success: false,
          message: 'Fund not found',
        }),
      } as Response);

      await expect(
        fundApi.createFundSnapshot('nonexistent-fund', '2024-12', {})
      ).rejects.toThrow();
    });
  });

  describe('deleteFundSnapshot', () => {
    it('should delete a fund snapshot successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Snapshot deleted successfully',
        data: {
          message: 'Snapshot deleted successfully',
          fund_id: 'test-fund-123',
          month: '2024-12',
        },
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await fundApi.deleteFundSnapshot('test-fund-123', '2024-12');

      expect(result.success).toBe(true);
      expect(result.data.message).toBe('Snapshot deleted successfully');

      // Verify the request was made with correct parameters
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/funds/test-fund-123/snapshots/2024-12'),
        expect.objectContaining({
          method: 'DELETE',
        })
      );
    });

    it('should handle snapshot not found for deletion', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => ({
          success: false,
          message: 'Snapshot not found',
        }),
      } as Response);

      await expect(fundApi.deleteFundSnapshot('test-fund-123', '2024-12')).rejects.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(fundApi.getFundSnapshot('test-fund-123', '2024-12')).rejects.toThrow(
        'Network error'
      );
    });

    it('should handle server errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({
          success: false,
          message: 'Internal server error',
        }),
      } as Response);

      await expect(fundApi.getFundSnapshot('test-fund-123', '2024-12')).rejects.toThrow();
    });

    it('should handle malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON');
        },
      } as Response);

      await expect(fundApi.getFundSnapshot('test-fund-123', '2024-12')).rejects.toThrow();
    });
  });

  describe('Request Logging', () => {
    it('should log API requests', async () => {
      const consoleSpy = jest.spyOn(console, 'log');

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          data: {},
        }),
      } as Response);

      await fundApi.getFundSnapshot('test-fund-123', '2024-12');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('getFundSnapshot called'),
        'test-fund-123',
        '2024-12'
      );
    });

    it('should log API responses', async () => {
      const consoleSpy = jest.spyOn(console, 'log');
      const mockResponse = { success: true, data: {} };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      await fundApi.getFundSnapshot('test-fund-123', '2024-12');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('response received'),
        mockResponse
      );
    });
  });
});
