/**
 * Test suite for MonthSelector component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import MonthSelector from '../MonthSelector';

describe('MonthSelector', () => {
  const mockOnChange = jest.fn();
  const defaultProps = {
    onChange: mockOnChange,
    availableMonths: ['2024-12', '2024-11', '2024-10'],
  };

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('renders with placeholder text', () => {
    render(<MonthSelector {...defaultProps} placeholder="Select a month" />);
    expect(screen.getByText('Select a month')).toBeInTheDocument();
  });

  it('displays selected month correctly', () => {
    render(<MonthSelector {...defaultProps} value="2024-12" />);
    expect(screen.getByText('December 2024')).toBeInTheDocument();
  });

  it('shows current month indicator', () => {
    const currentMonth = new Date().toISOString().slice(0, 7);
    render(<MonthSelector {...defaultProps} value={currentMonth} />);
    expect(screen.getByText('Current')).toBeInTheDocument();
  });

  it('shows data availability indicators', () => {
    render(<MonthSelector {...defaultProps} value="2024-12" />);
    
    // Click to open dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Check for data indicators (green dots)
    const dataIndicators = screen.getAllByTitle('Has snapshot data');
    expect(dataIndicators.length).toBeGreaterThan(0);
  });

  it('opens dropdown when clicked', () => {
    render(<MonthSelector {...defaultProps} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    // Should show month options
    expect(screen.getByText(/December 2024/)).toBeInTheDocument();
  });

  it('calls onChange when month is selected', async () => {
    render(<MonthSelector {...defaultProps} />);
    
    // Open dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Click on a month option
    const monthOption = screen.getByText(/December 2024/);
    fireEvent.click(monthOption);
    
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith('2024-12');
    });
  });

  it('closes dropdown when clicking outside', async () => {
    render(
      <div>
        <MonthSelector {...defaultProps} />
        <div data-testid="outside">Outside element</div>
      </div>
    );
    
    // Open dropdown
    fireEvent.click(screen.getByRole('button'));
    expect(screen.getByText(/December 2024/)).toBeInTheDocument();
    
    // Click outside
    fireEvent.mouseDown(screen.getByTestId('outside'));
    
    await waitFor(() => {
      expect(screen.queryByText(/December 2024/)).not.toBeInTheDocument();
    });
  });

  it('closes dropdown when pressing Escape', async () => {
    render(<MonthSelector {...defaultProps} />);
    
    // Open dropdown
    fireEvent.click(screen.getByRole('button'));
    expect(screen.getByText(/December 2024/)).toBeInTheDocument();
    
    // Press Escape
    fireEvent.keyDown(document, { key: 'Escape' });
    
    await waitFor(() => {
      expect(screen.queryByText(/December 2024/)).not.toBeInTheDocument();
    });
  });

  it('handles keyboard navigation', () => {
    render(<MonthSelector {...defaultProps} />);
    
    const button = screen.getByRole('button');
    
    // Test Enter key
    fireEvent.keyDown(button, { key: 'Enter' });
    expect(screen.getByText(/December 2024/)).toBeInTheDocument();
    
    // Close dropdown
    fireEvent.keyDown(document, { key: 'Escape' });
    
    // Test Space key
    fireEvent.keyDown(button, { key: ' ' });
    expect(screen.getByText(/December 2024/)).toBeInTheDocument();
  });

  it('is disabled when disabled prop is true', () => {
    render(<MonthSelector {...defaultProps} disabled={true} />);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('opacity-50', 'cursor-not-allowed');
  });

  it('generates correct number of month options', () => {
    render(<MonthSelector {...defaultProps} maxMonths={6} />);
    
    // Open dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Should show 6 months (current month + 5 previous)
    const monthOptions = screen.getAllByText(/\d{4}/); // Find elements with year
    expect(monthOptions.length).toBeLessThanOrEqual(6);
  });

  it('highlights selected month in dropdown', () => {
    render(<MonthSelector {...defaultProps} value="2024-12" />);
    
    // Open dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Find the selected month option
    const selectedOption = screen.getByText('December 2024').closest('div');
    expect(selectedOption).toHaveClass('bg-blue-50');
  });

  it('shows check icon for selected month', () => {
    render(<MonthSelector {...defaultProps} value="2024-12" />);
    
    // Open dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Should show check icon for selected month
    const checkIcon = screen.getByRole('img', { hidden: true }); // SVG icons are hidden by default
    expect(checkIcon).toBeInTheDocument();
  });

  it('handles empty availableMonths array', () => {
    render(<MonthSelector {...defaultProps} availableMonths={[]} />);
    
    // Open dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Should still show month options, but without data indicators
    expect(screen.getByText(/December 2024/)).toBeInTheDocument();
    expect(screen.queryByTitle('Has snapshot data')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<MonthSelector {...defaultProps} className="custom-class" />);
    
    const container = screen.getByRole('button').closest('div');
    expect(container).toHaveClass('custom-class');
  });

  it('supports dark mode styling', () => {
    // Mock dark mode by adding dark class to document
    document.documentElement.classList.add('dark');
    
    render(<MonthSelector {...defaultProps} />);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('dark:bg-gray-800');
    
    // Cleanup
    document.documentElement.classList.remove('dark');
  });

  it('prevents future month selection', () => {
    const futureMonth = new Date();
    futureMonth.setMonth(futureMonth.getMonth() + 2);
    const futureMonthString = futureMonth.toISOString().slice(0, 7);
    
    render(<MonthSelector {...defaultProps} maxMonths={6} />);
    
    // Open dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Future months should not be available in the dropdown
    expect(screen.queryByText(futureMonth.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    }))).not.toBeInTheDocument();
  });

  it('handles month formatting correctly', () => {
    render(<MonthSelector {...defaultProps} value="2024-01" />);
    
    // Should display "January 2024" for "2024-01"
    expect(screen.getByText('January 2024')).toBeInTheDocument();
  });
});
