'use client'

import React, { useState, useRef, useEffect } from 'react'
import { ChevronDownIcon, CalendarIcon, CheckIcon } from '@heroicons/react/24/outline'

import { BaseComponentProps } from '@/types'

interface MonthOption {
  value: string // YYYY-MM format
  label: string // Display label like "January 2025"
  hasData?: boolean // Whether this month has snapshot data
  isCurrent?: boolean // Whether this is the current month
}

interface MonthSelectorProps extends BaseComponentProps {
  value?: string // Selected month in YYYY-MM format
  onChange: (month: string) => void
  availableMonths?: string[] // Months that have data
  disabled?: boolean
  placeholder?: string
  maxMonths?: number // Maximum number of months to show (default: 36)
}

const MonthSelector: React.FC<MonthSelectorProps> = ({
  value,
  onChange,
  availableMonths = [],
  disabled = false,
  placeholder = 'Select month',
  maxMonths = 36,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Generate month options for the last N months
  const generateMonthOptions = (): MonthOption[] => {
    const options: MonthOption[] = []
    const currentDate = new Date()
    const currentMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`

    for (let i = 0; i < maxMonths; i++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1)
      const monthValue = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      const monthLabel = date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long' 
      })

      options.push({
        value: monthValue,
        label: monthLabel,
        hasData: availableMonths.includes(monthValue),
        isCurrent: monthValue === currentMonth
      })
    }

    return options
  }

  const monthOptions = generateMonthOptions()
  const selectedOption = monthOptions.find(option => option.value === value)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen])

  const handleSelect = (monthValue: string) => {
    onChange(monthValue)
    setIsOpen(false)
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      setIsOpen(!isOpen)
    }
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        type="button"
        className={`
          relative w-full cursor-default rounded-lg border border-gray-300 dark:border-gray-600 
          bg-white dark:bg-gray-800 py-2 pl-3 pr-10 text-left shadow-sm 
          focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 dark:hover:border-gray-500'}
        `}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <span className="flex items-center">
          <CalendarIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 mr-2" />
          <span className="block truncate text-gray-900 dark:text-gray-100">
            {selectedOption ? (
              <span className="flex items-center">
                {selectedOption.label}
                {selectedOption.isCurrent && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                    Current
                  </span>
                )}
                {selectedOption.hasData && (
                  <span className="ml-2 h-2 w-2 rounded-full bg-green-500" title="Has data" />
                )}
              </span>
            ) : (
              placeholder
            )}
          </span>
        </span>
        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
          <ChevronDownIcon 
            className={`h-5 w-5 text-gray-400 dark:text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
            aria-hidden="true" 
          />
        </span>
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white dark:bg-gray-800 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          {monthOptions.map((option) => (
            <div
              key={option.value}
              className={`
                relative cursor-default select-none py-2 pl-3 pr-9 hover:bg-gray-100 dark:hover:bg-gray-700
                ${value === option.value ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
              `}
              onClick={() => handleSelect(option.value)}
            >
              <div className="flex items-center">
                <span className={`block truncate ${value === option.value ? 'font-semibold text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-gray-100'}`}>
                  {option.label}
                </span>
                
                {/* Current month indicator */}
                {option.isCurrent && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                    Current
                  </span>
                )}
                
                {/* Data availability indicator */}
                {option.hasData && (
                  <span 
                    className="ml-2 h-2 w-2 rounded-full bg-green-500" 
                    title="Has snapshot data"
                  />
                )}
              </div>

              {/* Selected indicator */}
              {value === option.value && (
                <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                  <CheckIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" aria-hidden="true" />
                </span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default MonthSelector
