'use client';

import { useState, useEffect } from 'react';

import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import MonthSelector from '@/components/ui/MonthSelector';
import { MarketDataInput, Fund, FundSnapshotCreate } from '@/types';
import { fundApi } from '@/lib/api';

interface MarketDataInputProps {
  fund: Fund;
  selectedMonth?: string; // YYYY-MM format
  availableMonths?: string[]; // Months that have snapshot data
  onSubmit: (month: string, data: Partial<MarketDataInput>) => Promise<void>;
  onCancel: () => void;
  className?: string;
}

export default function MarketDataInputForm({
  fund,
  selectedMonth,
  availableMonths = [],
  onSubmit,
  onCancel,
  className = ''
}: MarketDataInputProps) {
  const [currentMonth, setCurrentMonth] = useState<string>(
    selectedMonth || new Date().toISOString().slice(0, 7)
  );
  const [formData, setFormData] = useState<Partial<MarketDataInput>>({
    fundId: fund.id,
    dataTimestamp: new Date(),
    inputBy: '', // This should come from user context
    validated: false,
  });

  const [loading, setLoading] = useState(false);
  const [loadingSnapshot, setLoadingSnapshot] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load snapshot data when month changes
  useEffect(() => {
    const loadSnapshotData = async () => {
      if (!currentMonth) return;

      setLoadingSnapshot(true);
      try {
        const response = await fundApi.getFundSnapshot(fund.id, currentMonth);
        if (response.success && response.data) {
          // Populate form with snapshot data
          const snapshot = response.data;
          setFormData(prev => ({
            ...prev,
            nav: snapshot.nav ? parseFloat(snapshot.nav) : undefined,
            // Add other market data fields from snapshot
            ...(snapshot.market_data || {}),
          }));
        }
      } catch (error) {
        // Snapshot doesn't exist yet, which is fine for new data
        console.log('No existing snapshot for month:', currentMonth);
      } finally {
        setLoadingSnapshot(false);
      }
    };

    loadSnapshotData();
  }, [currentMonth, fund.id]);

  const handleInputChange = (field: keyof MarketDataInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.inputBy?.trim()) {
      newErrors.inputBy = 'Input by field is required';
    }
    
    if (!formData.dataTimestamp) {
      newErrors.dataTimestamp = 'Data timestamp is required';
    }
    
    // Validate numeric fields
    if (formData.nav !== undefined && formData.nav <= 0) {
      newErrors.nav = 'NAV must be greater than 0';
    }
    
    if (formData.marketPrice !== undefined && formData.marketPrice <= 0) {
      newErrors.marketPrice = 'Market price must be greater than 0';
    }
    
    if (formData.volume !== undefined && formData.volume < 0) {
      newErrors.volume = 'Volume cannot be negative';
    }
    
    if (formData.priceToBook !== undefined && formData.priceToBook < 0) {
      newErrors.priceToBook = 'P/B ratio cannot be negative';
    }
    
    if (formData.priceToEarnings !== undefined && formData.priceToEarnings < 0) {
      newErrors.priceToEarnings = 'P/E ratio cannot be negative';
    }
    
    if (formData.dividendYield !== undefined && formData.dividendYield < 0) {
      newErrors.dividendYield = 'Dividend yield cannot be negative';
    }
    
    if (formData.volatility !== undefined && formData.volatility < 0) {
      newErrors.volatility = 'Volatility cannot be negative';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!currentMonth) {
      setErrors({ submit: 'Please select a month' });
      return;
    }

    setLoading(true);
    try {
      await onSubmit(currentMonth, formData);
    } catch (error) {
      console.error('Error submitting market data:', error);
      setErrors({ submit: 'Failed to submit market data. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <Card>
        <Card.Header>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Monthly Market Data Input - {fund.name}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Input monthly market data for {fund.symbol}. Market data is updated on a monthly basis.
          </p>
        </Card.Header>
        
        <Card.Content>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Input By *
                </label>
                <input
                  type="text"
                  value={formData.inputBy || ''}
                  onChange={(e) => handleInputChange('inputBy', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                  placeholder="Your name or ID"
                />
                {errors.inputBy && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.inputBy}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Monthly Data Period *
                </label>
                <MonthSelector
                  value={currentMonth}
                  onChange={(month) => {
                    setCurrentMonth(month);
                    // Update the dataTimestamp in formData
                    const [year, monthNum] = month.split('-');
                    const date = new Date(parseInt(year), parseInt(monthNum) - 1, 1);
                    handleInputChange('dataTimestamp', date);
                  }}
                  availableMonths={availableMonths}
                  disabled={loadingSnapshot}
                  placeholder="Select month for data input"
                />
                {loadingSnapshot && (
                  <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                    Loading existing data for {currentMonth}...
                  </p>
                )}
                {!loadingSnapshot && availableMonths.includes(currentMonth) && (
                  <p className="mt-1 text-xs text-green-600 dark:text-green-400">
                    ✓ Data exists for {currentMonth} - editing existing snapshot
                  </p>
                )}
                {!loadingSnapshot && !availableMonths.includes(currentMonth) && (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Creating new snapshot for {currentMonth}
                  </p>
                )}
                {errors.dataTimestamp && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.dataTimestamp}</p>
                )}
              </div>
            </div>

            {/* Price Data Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Monthly Price Data</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    NAV
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.nav || ''}
                    onChange={(e) => handleInputChange('nav', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Monthly Net Asset Value"
                  />
                  {errors.nav && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.nav}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Market Price
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.marketPrice || ''}
                    onChange={(e) => handleInputChange('marketPrice', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Month-end market price"
                  />
                  {errors.marketPrice && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.marketPrice}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Volume
                  </label>
                  <input
                    type="number"
                    value={formData.volume || ''}
                    onChange={(e) => handleInputChange('volume', parseInt(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Monthly trading volume"
                  />
                  {errors.volume && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.volume}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Valuation Metrics Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Monthly Valuation Metrics</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    P/B Ratio
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.priceToBook || ''}
                    onChange={(e) => handleInputChange('priceToBook', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Price-to-Book ratio"
                  />
                  {errors.priceToBook && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.priceToBook}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    P/E Ratio
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.priceToEarnings || ''}
                    onChange={(e) => handleInputChange('priceToEarnings', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Price-to-Earnings ratio"
                  />
                  {errors.priceToEarnings && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.priceToEarnings}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Dividend Yield (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.dividendYield || ''}
                    onChange={(e) => handleInputChange('dividendYield', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Dividend yield percentage"
                  />
                  {errors.dividendYield && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.dividendYield}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Risk Metrics Section */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Monthly Risk Metrics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Volatility (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.volatility || ''}
                    onChange={(e) => handleInputChange('volatility', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Volatility percentage"
                  />
                  {errors.volatility && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.volatility}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Beta
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.beta || ''}
                    onChange={(e) => handleInputChange('beta', parseFloat(e.target.value) || undefined)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                    placeholder="Beta coefficient"
                  />
                  {errors.beta && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.beta}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Notes Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes
              </label>
              <textarea
                value={formData.notes || ''}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                placeholder="Additional notes about this monthly data input..."
              />
            </div>

            {/* Validation */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="validated"
                checked={formData.validated || false}
                onChange={(e) => handleInputChange('validated', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="validated" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                Mark this data as validated
              </label>
            </div>

            {formData.validated && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Validation Notes
                </label>
                <textarea
                  value={formData.validationNotes || ''}
                  onChange={(e) => handleInputChange('validationNotes', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                  placeholder="Validation notes..."
                />
              </div>
            )}

            {/* Error Display */}
            {errors.submit && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <p className="text-sm text-red-600 dark:text-red-400">{errors.submit}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
              >
                {loading ? 'Submitting...' : 'Submit Market Data'}
              </Button>
            </div>
          </form>
        </Card.Content>
      </Card>
    </div>
  );
}
